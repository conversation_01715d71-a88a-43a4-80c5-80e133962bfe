output "sdk_domains_mapping" {
  value = [for index in range(length(var.sdk_domains)) : {
    "endpoint" : "${tolist(aws_acm_certificate.aws_acm_cert[index].domain_validation_options)[0].domain_name}",
    "type" : "${tolist(aws_acm_certificate.aws_acm_cert[index].domain_validation_options)[0].resource_record_type}",
    "record_name" : "${tolist(aws_acm_certificate.aws_acm_cert[index].domain_validation_options)[0].resource_record_name}",
    "record_value" : "${tolist(aws_acm_certificate.aws_acm_cert[index].domain_validation_options)[0].resource_record_value}"
    }
  ]
}

output "loadbalancer" {
  value = data.aws_lb.lb.dns_name
}
