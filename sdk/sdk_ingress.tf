resource "kubernetes_ingress_v1" "sdk_ingress" {
  count = length(var.sdk_domains)
  metadata {
    name = "sdk-${replace(var.sdk_domains[count.index].endpoint, ".", "-")}"
    annotations = {
      "nginx.ingress.kubernetes.io/server-snippet" = <<EOF
      location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\nDisallow: /\n";
      }
      EOF
    }
  }

  spec {
    rule {
      host = var.sdk_domains[count.index].endpoint
      http {
        path {
          backend {
            service {
              name = "blotoutsdk"
              port {
                number = 8080
              }
            }
          }
          path      = "/sdk"
          path_type = "ImplementationSpecific"
        }
      }
    }
  }
}