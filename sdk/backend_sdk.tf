# backend_SDK deploy on Kubernetes. #

resource "kubernetes_config_map" "sdk-configmap" {
  metadata {
    name = "sdk-configmap"
  }

  data = {
    SERVER_PORT           = "8080"
    LOGGING_FILE          = "/tmp/access.log"
    AWS_ACCESS_KEY_ID     = "${var.aws_access_key}"
    AWS_SECRET_ACCESS_KEY = "${var.aws_secret_key}"
    AWS_DEFAULT_REGION    = "${var.aws_region}"
    REDIS_CLUSTER_NODES   = "${data.aws_secretsmanager_secret_version.redis.secret_string}:6379"
    FIREHOSE_COUNT        = "${var.firehose_count}"
    ORGANIZATION_ID       = "${var.organization_name}"
    ENV                   = "${var.environment}"
  }
}

resource "helm_release" "backend_sdk" {

  name = "backend-sdk"

  repository = "https://blotoutio.github.io/charts"
  chart      = "backend-sdk"
  version    = var.backend_sdk["chart_version"]

  create_namespace = "true"
  namespace        = "default"
  cleanup_on_fail  = true

  lifecycle {
    replace_triggered_by = [
      # Replace `aws_appautoscaling_target` each time this instance of
      # the `aws_ecs_service` is replaced.
      kubernetes_config_map.sdk-configmap
    ]
  }

  set {
    name  = "image.repository"
    value = var.backend_sdk["image"]["repository"]
  }
  set {
    name  = "image.tag"
    value = var.backend_sdk["image"]["version"]
  }

  set {
    name  = "autoscaling.enabled"
    value = "true"
    type  = "auto"
  }

  set {
    name  = "autoscaling.targetCPUUtilizationPercentage"
    value = "50"
  }

  set {
    name  = "autoscaling.targetMemoryUtilizationPercentage"
    value = "60"
  }

  set {
    name  = "autoscaling.maxReplicas"
    value = "2"
  }

  set {
    name  = "ingress.enabled"
    value = false
  }

  # set {
  #   name  = "ingress.hosts[0]"
  #   value = "${var.sdk_domain}"
  # }

  # set {
  #   name  = "ingress.paths[0]"
  #   value = "/sdk"
  # }

  set {
    name  = "livenessProbe.httpGet.path"
    value = "/sdk/v1/healthcheck"
  }

  set {
    name  = "nameOverride"
    value = "blotoutsdk"
  }

  set {
    name  = "fullnameOverride"
    value = "blotoutsdk"
  }

  set {
    name  = "readinessProbe.httpGet.path"
    value = "/sdk/v1/healthcheck"
  }

  set {
    name  = "service.port"
    value = "8080"
  }

  set {
    name  = "imagePullSecrets[0].name"
    value = "regcred"
  }

  set {
    name  = "env.LOGGING_LEVEL_COM_BLOT"
    value = var.logging_level_com_blot
  }

  set {
    name  = "resources.limits.cpu"
    value = "1000m"
  }

  set {
    name  = "resources.limits.memory"
    value = "4Gi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "250m"
  }

  set {
    name  = "resources.requests.memory"
    value = "600Mi"
  }

}