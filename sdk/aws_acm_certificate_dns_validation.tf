locals {
  true_ack_index = [for index in range(length(var.sdk_domains)) : tostring(index) if var.sdk_domains[index].dns_map_ack_success]
}

resource "aws_acm_certificate" "aws_acm_cert" {
  count             = length(var.sdk_domains)
  domain_name       = var.sdk_domains[count.index].endpoint
  validation_method = "DNS"

  tags = {
    Name        = var.organization_name
    owner       = var.organization_name
    terraform   = "true"
    Environment = var.environment
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate_validation" "validation" {
  timeouts {
    create = "10m"
  }
  for_each        = toset(local.true_ack_index)
  certificate_arn = aws_acm_certificate.aws_acm_cert[tonumber(each.value)].arn
}

data "aws_lb" "lb" {
  tags = {
    "kubernetes.io/service-name"                                        = "ingress-nginx/nginx-ingress-controller"
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "owned"
  }
}

data "aws_lb_listener" "lb_listener" {
  load_balancer_arn = data.aws_lb.lb.arn
  port              = 443
}

resource "aws_lb_listener_certificate" "add_lb_listener_cert" {
  for_each        = toset(local.true_ack_index)
  listener_arn    = data.aws_lb_listener.lb_listener.arn
  certificate_arn = aws_acm_certificate.aws_acm_cert[tonumber(each.value)].arn
}
