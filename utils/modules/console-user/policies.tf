resource "aws_iam_user_policy" "s3_policy" {
  count = contains(var.policies, "s3") ? 1 : 0
  name  = "S3FullAccessPolicy"
  user  = var.username

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:*",
        ]
        Effect   = "Allow"
        Resource = "*"
      },
    ]
  })
}

resource "aws_iam_user_policy" "athena_policy" {
  count = contains(var.policies, "athena") ? 1 : 0
  name  = "AthenFullAccessPolicy"
  user  = var.username

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "athena:*",
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "glue:*",
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "admin_policy" {
  count      = contains(var.policies, "admin") ? 1 : 0
  user       = aws_iam_user.console_user.name
  policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
}