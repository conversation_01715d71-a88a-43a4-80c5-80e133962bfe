# Mapping for AWS ACM certificates
output "cdn_domains_mappings" {
  value = [for domain, _ in var.cdn_domains : {
    "endpoint" : "${tolist(aws_acm_certificate.aws_acm_cert[domain].domain_validation_options)[0].domain_name}",
    "type" : "${tolist(aws_acm_certificate.aws_acm_cert[domain].domain_validation_options)[0].resource_record_type}",
    "record_name" : "${tolist(aws_acm_certificate.aws_acm_cert[domain].domain_validation_options)[0].resource_record_name}",
    "record_value" : "${tolist(aws_acm_certificate.aws_acm_cert[domain].domain_validation_options)[0].resource_record_value}"
    }
  ]
}

# Mapping for cloudfront domain
output "cdn_cloudfront_mappings" {

  value = [for domain in local.acknowledged_domains : {
    "domain" : var.cdn_domains[domain].endpoint,
    "cloudfront_endpoint" : aws_cloudfront_distribution.s3_distribution[domain].domain_name,
    "type" : "CNAME"
    }
  ]
}