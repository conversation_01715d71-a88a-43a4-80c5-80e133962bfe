provider "aws" {
  alias      = "cdn"
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
  region     = "us-east-1"
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  provider = aws.cdn
  for_each = toset(local.acknowledged_domains)
  origin {
    domain_name = aws_s3_bucket.cloudfront_bucket.bucket_regional_domain_name
    origin_id   = "${var.cdn_domains[each.value].endpoint}-origin-id" # unique ID that is required

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.cloudfront_oai.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "${var.cdn_domains[each.value].endpoint} - CDN (Created by - Blotout)"
  default_root_object = "index.html"

  aliases = [var.cdn_domains[each.value].endpoint]

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "${var.cdn_domains[each.value].endpoint}-origin-id"

    forwarded_values {
      query_string = true

      cookies {
        forward = "all"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  price_class = "PriceClass_All"

  # Atleast one restriction block is required
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = aws_acm_certificate.aws_acm_cert[each.value].arn
    ssl_support_method             = "sni-only"
    cloudfront_default_certificate = false
  }
  tags = local.custom_tags
}