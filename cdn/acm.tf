resource "aws_acm_certificate" "aws_acm_cert" {
  provider          = aws.cdn
  for_each          = var.cdn_domains
  domain_name       = each.value.endpoint
  validation_method = "DNS"
  tags              = local.custom_tags
}

resource "aws_acm_certificate_validation" "validation" {
  provider = aws.cdn
  timeouts {
    create = "10m"
  }
  for_each        = toset(local.acknowledged_domains)
  certificate_arn = aws_acm_certificate.aws_acm_cert[each.value].arn
}
