variable "aws_region" {
  default = ""
}

variable "aws_access_key" {
  default = ""
}

variable "aws_secret_key" {
  default = ""
}

variable "organization_name" {
  default = ""
}

variable "environment" {
  default     = ""
  description = "define your environment ex. dev or prod"
}

variable "created_by" {
  default     = "Blotout"
  description = "Name of the organization which is creating a resource"
}

variable "flow" {
  default     = "cdn"
  description = "The application name to identify which flow created a resource"
}

variable "project" {
  default = "self-serve"
}

variable "sdk_version" {
  default = ""
}

variable "cdn_domains" {
  default = {

  }
}
