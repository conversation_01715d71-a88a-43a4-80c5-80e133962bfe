resource "aws_s3_bucket" "cloudfront_bucket" {
  provider      = aws.cdn
  bucket        = local.cloudflare_bucket
  force_destroy = true
  tags          = local.custom_tags
}

resource "aws_s3_bucket_acl" "cloudfrontbucket_acl" {
  provider = aws.cdn
  bucket   = aws_s3_bucket.cloudfront_bucket.id
  acl      = "private"
}

resource "aws_s3_bucket_public_access_block" "cloudfront_bucket_public_block" {
  provider = aws.cdn
  bucket   = aws_s3_bucket.cloudfront_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}


resource "aws_s3_bucket_website_configuration" "cloudfront_website" {
  provider = aws.cdn
  bucket   = aws_s3_bucket.cloudfront_bucket.bucket

  index_document {
    suffix = "index.js"
  }

  error_document {
    key = "error.html"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "cloudfront_bucket_server_side_encryption" {
  provider = aws.cdn
  bucket   = aws_s3_bucket.cloudfront_bucket.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# OAI

# Step 1. Create an OAI
resource "aws_cloudfront_origin_access_identity" "cloudfront_oai" {
  provider = aws.cdn
  comment  = "Creating OAI for bucket"
}

# Step 2. Creating the OAI arn in IAM policy 
data "aws_iam_policy_document" "cloudfront_bucket_policy-doc" {
  provider = aws.cdn
  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.cloudfront_oai.iam_arn]
    }

    actions = [
      "s3:GetObject"
    ]

    resources = ["${aws_s3_bucket.cloudfront_bucket.arn}/*"]
  }
}

# Step 3. Attach the polcy to the bucket
resource "aws_s3_bucket_policy" "cloudfront_bucket_policy" {
  provider = aws.cdn
  bucket   = aws_s3_bucket.cloudfront_bucket.id
  policy   = data.aws_iam_policy_document.cloudfront_bucket_policy-doc.json
}