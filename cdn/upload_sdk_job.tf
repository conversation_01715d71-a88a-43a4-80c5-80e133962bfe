resource "kubernetes_job" "cdn_upload" {
  depends_on = [
    aws_s3_bucket.cloudfront_bucket
  ]
  metadata {
    name      = "cdn-upload-job"
    namespace = "default"
  }
  spec {
    template {
      metadata {}
      spec {
        image_pull_secrets {
          name = "regcred"
        }
        container {
          name              = "cdn-job"
          image             = "blotout/infra_script:0.20.0"
          image_pull_policy = "IfNotPresent"
          command           = ["python3", "cdn.py"]

          env {
            name  = "BUCKET_NAME"
            value = local.cloudflare_bucket
          }
          env {
            name  = "SDK_VERSION"
            value = var.sdk_version
          }
          env {
            name  = "AWS_ACCESS_KEY_ID"
            value = var.aws_access_key
          }
          env {
            name  = "AWS_SECRET_ACCESS_KEY"
            value = var.aws_secret_key
          }
          env {
            name  = "AWS_DEFAULT_REGION"
            value = var.aws_region
          }
        }
        restart_policy = "Never"
      }
    }
    backoff_limit = 4
  }
  wait_for_completion = false
}