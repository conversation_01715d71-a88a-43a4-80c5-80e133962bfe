variable "aws_region" {
  default = "us-east-1"
}

variable "aws_access_key" {
  default = ""
}

variable "aws_secret_key" {
  default = ""
}

variable "organization_name" {
  default     = ""
  description = "name of member organization to be created"
}

variable "organization_mail" {
  default     = ""
  description = "root email of member organization"
}

variable "organization_region" {
  default     = ""
  description = "organization region"
}

variable "role_name" {
  default     = "self-serve-role"
  description = "role name for member organization"
}

variable "environment" {
  default     = ""
  description = "define your environment ex. dev or prod"
}

variable "created_by" {
  default     = "Blotout"
  description = "Name of the organization which is creating a resource"
}

variable "flow" {
  default     = "admin"
  description = "The application name to identify which flow created a resource"
}

variable "project" {
  default = "self-serve"
}

# variables for new account#
variable "iam_user" {
  default     = "blotout-k8s"
  description = "name of user in member organization"
}

variable "public_key" {
  default     = ""
  description = "key used for encryption of secrets"
}