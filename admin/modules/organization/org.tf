resource "aws_organizations_account" "member_account" {
  name                       = var.organization_name
  email                      = var.organization_mail
  role_name                  = var.role_name
  iam_user_access_to_billing = "ALLOW"
  close_on_deletion          = "true"
  tags                       = var.tags
  lifecycle {
    ignore_changes = [role_name]
  }
}
provider "aws" {
  assume_role {
    role_arn = "arn:aws:iam::${aws_organizations_account.member_account.id}:role/${var.role_name}"
  }
  alias      = "member_account_provider"
  region     = var.aws_region
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
}