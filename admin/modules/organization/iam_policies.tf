data "aws_iam_policy_document" "s3_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "s3:GetLifecycleConfiguration",
      "s3:GetBucketTagging",
      "s3:DeleteObjectVersion",
      "s3:ListBucketVersions",
      "s3:GetBucketLogging",
      "s3:RestoreObject",
      "s3:CreateBucket",
      "s3:ListBucket",
      "s3:GetAccelerateConfiguration",
      "s3:GetBucketPolicy",
      "s3:PutEncryptionConfiguration",
      "s3:GetEncryptionConfiguration",
      "s3:GetBucketObjectLockConfiguration",
      "s3:DeleteBucketWebsite",
      "s3:PutBucketTagging",
      "s3:GetBucketRequestPayment",
      "s3:PutLifecycleConfiguration",
      "s3:PutBucketAcl",
      "s3:PutObjectTagging",
      "s3:DeleteObject",
      "s3:DeleteBucket",
      "s3:PutObjectAcl",
      "s3:GetBucketPublicAccessBlock",
      "s3:PutBucketPublicAccessBlock",
      "s3:GetBucketWebsite",
      "s3:GetBucketVersioning",
      "s3:GetBucketAcl",
      "s3:DeleteBucketPolicy",
      "s3:GetReplicationConfiguration",
      "s3:PutObject",
      "s3:GetObject",
      "s3:PutBucketWebsite",
      "s3:GetBucketCORS",
      "s3:PutBucketPolicy",
      "s3:GetBucketLocation",
      "s3:GetObjectVersion"
    ]

    resources = [
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-airbyte-logs",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-assets",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-athena-logs",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-emr",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-landing",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-processed",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-stg",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-outbound",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-airbyte-logs/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-assets/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-athena-logs/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-emr/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-landing/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-processed/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-stg/*",
      "arn:aws:s3:::b-${var.organization_name}-${var.environment}-outbound/*"
    ]

  }
  statement {
    effect = "Allow"
    actions = [
      "s3:ListAllMyBuckets"
    ]
    resources = [
      "*"
    ]
  }
}

data "aws_iam_policy_document" "iam_policy_document" {
  statement {
    effect    = "Allow"
    actions   = [
                "iam:PutRolePolicy",
                "iam:CreateInstanceProfile",
                "iam:GetPolicyVersion",
                "iam:TagRole",
                "iam:TagPolicy",
                "iam:TagUser",
                "iam:UntagRole",
                "iam:UntagPolicy",
                "iam:UntagUser",
                "iam:RemoveRoleFromInstanceProfile",
                "iam:DeletePolicy",
                "iam:CreateRole",
                "iam:AttachRolePolicy",
                "iam:ListServiceSpecificCredentials",
                "iam:ListMFADevices",
                "iam:ListSigningCertificates",
                "iam:CreateUser",
                "iam:AddRoleToInstanceProfile",
                "iam:CreateLoginProfile",
                "iam:ListInstanceProfilesForRole",
                "iam:PassRole",
                "iam:ListSSHPublicKeys",
                "iam:DetachRolePolicy",
                "iam:ListAttachedRolePolicies",
                "iam:ListRolePolicies",
                "iam:DeleteOpenIDConnectProvider",
                "iam:DeleteLoginProfile",
                "iam:ListAccessKeys",
                "iam:DeleteInstanceProfile",
                "iam:GetRole",
                "iam:GetInstanceProfile",
                "iam:GetPolicy",
                "iam:DeleteUserPolicy",
                "iam:DeleteRole",
                "iam:DeleteUser",
                "iam:CreateOpenIDConnectProvider",
                "iam:CreatePolicy",
                "iam:CreatePolicyVersion",
                "iam:DeletePolicyVersion",
                "iam:SetDefaultPolicyVersion",
                "iam:CreateServiceLinkedRole",
                "iam:ListPolicyVersions",
                "iam:GetUserPolicy",
                "iam:ListGroupsForUser",
                "iam:PutUserPolicy",
                "iam:GetUser",
                "iam:GetOpenIDConnectProvider",
                "iam:GetLoginProfile"
            ]

    resources = [
      "arn:aws:iam::${aws_organizations_account.member_account.id}:instance-profile/*",
      "arn:aws:iam::${aws_organizations_account.member_account.id}:role/*",
      "arn:aws:iam::${aws_organizations_account.member_account.id}:user/*",
      "arn:aws:iam::${aws_organizations_account.member_account.id}:oidc-provider/*",
      "arn:aws:iam::${aws_organizations_account.member_account.id}:policy/*"
    ]

  }

  statement {
    effect = "Allow"
    actions = [
      "iam:ListVirtualMFADevices"
    ]
    resources = [
      "*"
    ]
  }
}

data "aws_iam_policy_document" "eks_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "eks:UpdateClusterVersion",
      "acm:DeleteCertificate",
      "eks:UpdateAddon",
      "eks:ListAddons",
      "eks:UpdateClusterConfig",
      "elasticloadbalancing:RemoveListenerCertificates",
      "eks:DescribeAddon",
      "eks:UpdateNodegroupVersion",
      "eks:DescribeNodegroup",
      "eks:ListUpdates",
      "acm:ImportCertificate",
      "elasticloadbalancing:AddListenerCertificates",
      "eks:ListNodegroups",
      "acm:AddTagsToCertificate",
      "eks:CreateNodegroup",
      "acm:ListTagsForCertificate",
      "eks:DeleteCluster",
      "acm:DescribeCertificate",
      "eks:DeleteAddon",
      "eks:DeleteNodegroup",
      "eks:DescribeUpdate",
      "eks:TagResource",
      "eks:CreateAddon",
      "eks:UpdateNodegroupConfig",
      "eks:DescribeCluster"
    ]

    resources = [
      "arn:aws:acm:${var.organization_region}:${aws_organizations_account.member_account.id}:certificate/*",
      "arn:aws:elasticloadbalancing:${var.organization_region}:${aws_organizations_account.member_account.id}:listener/net/*/*/*",
      "arn:aws:elasticloadbalancing:${var.organization_region}:${aws_organizations_account.member_account.id}:listener/app/*/*/*",
      "arn:aws:eks:${var.organization_region}:${aws_organizations_account.member_account.id}:addon/${var.organization_name}_${var.environment}/*/*",
      "arn:aws:eks:${var.organization_region}:${aws_organizations_account.member_account.id}:nodegroup/${var.organization_name}_${var.environment}/managed-node/*",
    "arn:aws:eks:${var.organization_region}:${aws_organizations_account.member_account.id}:cluster/${var.organization_name}_${var.environment}"]
  }
  statement {
    effect = "Allow"
    actions = [
      "ec2:AuthorizeSecurityGroupIngress",
      "ec2:DeleteSubnet",
      "ec2:DeleteVpcEndpoints",
      "ec2:CreateNatGateway",
      "ec2:CreateVpc",
      "ec2:AttachInternetGateway",
      "ec2:DescribeVpcAttribute",
      "ec2:DeleteRouteTable",
      "ec2:ModifySubnetAttribute",
      "ec2:AssociateRouteTable",
      "ec2:CreateRoute",
      "ec2:CreateInternetGateway",
      "ec2:RevokeSecurityGroupEgress",
      "ec2:CreateSecurityGroup",
      "ec2:ModifyVpcAttribute",
      "ec2:DeleteInternetGateway",
      "ec2:ReleaseAddress",
      "ec2:AuthorizeSecurityGroupEgress",
      "ec2:ImportKeyPair",
      "ec2:CreateTags",
      "ec2:DeleteRoute",
      "ec2:CreateRouteTable",
      "ec2:DetachInternetGateway",
      "ec2:DisassociateRouteTable",
      "ec2:AllocateAddress",
      "ec2:RevokeSecurityGroupIngress",
      "ec2:CreateVpcEndpoint",
      "ec2:DeleteSecurityGroup",
      "ec2:DeleteNatGateway",
      "ec2:DeleteVpc",
      "ec2:CreateSubnet",
      "ec2:DeleteKeyPair",
      "ec2:ModifyVpcEndpoint"
    ]

    resources = [
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:subnet/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:internet-gateway/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:elastic-ip/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:key-pair/${var.organization_name}_${var.environment}",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:security-group-rule/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:vpc-endpoint/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:route-table/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:natgateway/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:security-group/*",
      "arn:aws:ec2:${var.organization_region}:${aws_organizations_account.member_account.id}:vpc/*"
    ]

  }
  statement {
    effect = "Allow"
    actions = [
      "ec2:DescribeAddresses",
      "ec2:DescribeInternetGateways",
      "elasticloadbalancing:DescribeLoadBalancers",
      "elasticloadbalancing:DescribeListeners",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribeAvailabilityZones",
      "acm:RequestCertificate",
      "ec2:DescribeAccountAttributes",
      "acm:ListCertificates",
      "elasticloadbalancing:DescribeListenerCertificates",
      "eks:DescribeAddonVersions",
      "ec2:DescribeKeyPairs",
      "ec2:DescribeNetworkAcls",
      "ec2:DescribeRouteTables",
      "eks:CreateCluster",
      "elasticloadbalancing:DescribeTags",
      "ec2:DescribeVpcClassicLinkDnsSupport",
      "ec2:DescribeNatGateways",
      "ec2:DescribePrefixLists",
      "ec2:DescribeSecurityGroups",
      "ec2:DescribeVpcClassicLink",
      "elasticloadbalancing:DescribeLoadBalancerAttributes",
      "ec2:DescribeVpcs",
      "ec2:DescribeVpcEndpoints",
      "ec2:DescribeSubnets"
    ]

    resources = ["*"]
  }
  statement {
    effect = "Allow"
    actions = [
      "elasticloadbalancing:AddListenerCertificates",
      "elasticloadbalancing:RemoveListenerCertificates"
    ]

    resources = [
      "arn:aws:elasticloadbalancing:${var.organization_region}:${aws_organizations_account.member_account.id}:listener/net/*/*/*",
      "arn:aws:elasticloadbalancing:${var.organization_region}:${aws_organizations_account.member_account.id}:listener/app/*/*/*"
    ]

  }
}

data "aws_iam_policy_document" "db_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "rds:AddTagsToResource",
      "elasticache:DescribeReplicationGroups",
      "rds:CreateDBParameterGroup",
      "rds:DescribeDBSubnetGroups",
      "rds:DescribeDBParameterGroups",
      "elasticache:CreateReplicationGroup",
      "elasticache:AddTagsToResource",
      "rds:CreateDBSubnetGroup",
      "elasticache:DeleteReplicationGroup",
      "rds:DeleteDBSubnetGroup",
      "rds:ListTagsForResource",
      "elasticache:DescribeCacheSubnetGroups",
      "rds:CreateDBInstance",
      "elasticache:CreateCacheSubnetGroup",
      "rds:CopyDBParameterGroup",
      "rds:DescribeDBInstances",
      "rds:DeleteDBParameterGroup",
      "rds:DescribeDBParameters",
      "elasticache:DescribeCacheClusters",
      "elasticache:DeleteCacheSubnetGroup",
      "elasticache:ListTagsForResource",
      "rds:DeleteDBInstance"
    ]

    resources = [
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:reserved-instance:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:parametergroup:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:securitygroup:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:subnetgroup:${var.organization_name}-${var.environment}",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:user:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:replicationgroup:${var.organization_name}-${var.environment}",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:cluster:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:snapshot:*",
      "arn:aws:elasticache:${var.organization_region}:${aws_organizations_account.member_account.id}:usergroup:*",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:db:*",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:secgrp:*",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:og:*",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:pg:${var.organization_name}-${var.environment}",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:subgrp:${var.organization_name}_${var.environment}",
      "arn:aws:rds:${var.organization_region}:${aws_organizations_account.member_account.id}:cluster:${var.organization_name}-${var.environment}"
    ]

  }
}

data "aws_iam_policy_document" "cdn_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "acm:DeleteCertificate",
      "acm:DescribeCertificate",
      "acm:AddTagsToCertificate",
      "acm:ImportCertificate",
      "acm:ListTagsForCertificate"
    ]
    resources = ["arn:aws:acm:us-east-1:${aws_organizations_account.member_account.id}:certificate/*"]
  }
  statement {
    effect = "Allow"
    actions = [
      "cloudfront:ListCloudFrontOriginAccessIdentities",
      "cloudfront:TagResource",
      "cloudfront:DeleteCloudFrontOriginAccessIdentity",
      "cloudfront:CreateDistribution",
      "cloudfront:CreateCloudFrontOriginAccessIdentity",
      "cloudfront:GetDistribution",
      "cloudfront:ListTagsForResource",
      "acm:RequestCertificate",
      "cloudfront:ListDistributions",
      "cloudfront:GetCloudFrontOriginAccessIdentity",
      "cloudfront:UpdateCloudFrontOriginAccessIdentity",
      "cloudfront:UpdateDistribution",
      "cloudfront:DeleteDistribution"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "extra_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "athena:StartQueryExecution",
      "athena:GetQueryExecution",
      "athena:GetQueryResults",
      "athena:GetTableMetadata",
      "athena:ListTableMetadata",
      "secretsmanager:CreateSecret",
      "secretsmanager:DescribeSecret",
      "secretsmanager:PutSecretValue",
      "secretsmanager:GetSecretValue",
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:DeleteSecret",
      "firehose:CreateDeliveryStream",
      "firehose:DescribeDeliveryStream",
      "firehose:DeleteDeliveryStream",
      "firehose:StopDeliveryStreamEncryption",
      "firehose:StartDeliveryStreamEncryption",
      "firehose:PutRecord",
      "firehose:ListTagsForDeliveryStream",
      "firehose:TagDeliveryStream",
      "kms:Decrypt",
      "kms:GenerateDataKey",
      "kms:DescribeKey",
      "kms:CreateGrant",
      "glue:CreateTable",
      "glue:GetTables",
      "glue:GetTable",
      "glue:UpdateTable",
      "glue:DeleteTable",
      "glue:CreateDatabase",
      "glue:GetDatabases",
      "glue:GetDatabase",
      "glue:UpdateDatabase",
      "glue:DeleteDatabase",
      "glue:GetPartition",
      "glue:GetPartitions",
      "glue:CreatePartition",
      "glue:UpdatePartition",
      "glue:DeletePartition",
      "glue:BatchGetPartition",
      "glue:BatchCreatePartition",
      "glue:BatchUpdatePartition",
      "glue:BatchDeletePartition",
      "glue:BatchGetCrawlers",
      "glue:GetCrawler",
      "glue:StartCrawler",
      "glue:UpdateCrawler",
      "glue:StopCrawler",
      "glue:DeleteCrawler",
      "glue:TagResource",
      "elasticmapreduce:AddJobFlowSteps",
      "elasticmapreduce:DescribeStep",
      "elasticmapreduce:DescribeCluster",
      "elasticmapreduce:AddTags"
    ]
    resources = [
      "arn:aws:kms:${var.organization_region}:${aws_organizations_account.member_account.id}:key/*",
      "arn:aws:secretsmanager:${var.organization_region}:${aws_organizations_account.member_account.id}:secret:*",
      "arn:aws:firehose:${var.organization_region}:${aws_organizations_account.member_account.id}:deliverystream/*",
      "arn:aws:athena:${var.organization_region}:${aws_organizations_account.member_account.id}:workgroup/*",
      "arn:aws:athena:${var.organization_region}:${aws_organizations_account.member_account.id}:datacatalog/*",
      "arn:aws:glue:${var.organization_region}:${aws_organizations_account.member_account.id}:catalog",
      "arn:aws:glue:${var.organization_region}:${aws_organizations_account.member_account.id}:table/*/*",
      "arn:aws:glue:${var.organization_region}:${aws_organizations_account.member_account.id}:database/*",
      "arn:aws:glue:${var.organization_region}:${aws_organizations_account.member_account.id}:crawler/*",
      "arn:aws:elasticmapreduce:${var.organization_region}:${aws_organizations_account.member_account.id}:cluster/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "sts:DecodeAuthorizationMessage",
      "sts:GetCallerIdentity",
      "route53:AssociateVPCWithHostedZone",
      "elasticmapreduce:DescribeSecurityConfiguration",
      "elasticmapreduce:ListSecurityConfigurations",
      "elasticmapreduce:ListClusters",
      "elasticmapreduce:CreateSecurityConfiguration",
      "elasticmapreduce:RunJobFlow",
      "firehose:ListDeliveryStreams",
      "glue:ListCrawlers",
      "glue:GetCrawlers",
      "glue:GetCrawlerMetrics",
      "glue:StartCrawlerSchedule",
      "glue:CreateCrawler",
      "ce:GetCostAndUsage"
    ]
    resources = ["*"]
  }
}