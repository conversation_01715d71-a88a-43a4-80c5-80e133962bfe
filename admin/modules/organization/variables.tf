variable "aws_region" {
  default     = ""
  description = "region in which the module will be deployed"
}

variable "aws_access_key" {
  default = ""
}

variable "aws_secret_key" {
  default = ""
}

variable "organization_name" {
  default     = ""
  description = "name of member organization to be created"
}

variable "environment" {
  default     = ""
  description = "define your environment ex. dev or prod"
}

variable "organization_region" {
  default     = ""
  description = "organization region"
}

variable "organization_mail" {
  default     = ""
  description = "root email of member organization"
}

variable "role_name" {
  default     = ""
  description = "role name for member organization"
}

variable "tags" {

}

# variables for new account#
variable "iam_user" {
  default     = ""
  description = "name of user in member organization"
}

variable "public_key" {
  default     = ""
  description = "key used for encryption of secrets"
}