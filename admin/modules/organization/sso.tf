data "aws_ssoadmin_instances" "sso" {}

data "aws_ssoadmin_permission_set" "production_permission_set" {
  instance_arn = tolist(data.aws_ssoadmin_instances.sso.arns)[0]
  name         = "Production"
}

data "aws_identitystore_group" "production_identitystore_group" {
  identity_store_id = tolist(data.aws_ssoadmin_instances.sso.identity_store_ids)[0]

  filter {
    attribute_path  = "DisplayName"
    attribute_value = "Production"
  }
}

resource "aws_ssoadmin_account_assignment" "production_sso" {
  instance_arn       = data.aws_ssoadmin_permission_set.production_permission_set.instance_arn
  permission_set_arn = data.aws_ssoadmin_permission_set.production_permission_set.arn
  principal_id       = data.aws_identitystore_group.production_identitystore_group.group_id
  principal_type     = "GROUP"
  target_id          = aws_organizations_account.member_account.id
  target_type        = "AWS_ACCOUNT"
}

data "aws_ssoadmin_permission_set" "admin_permission_set" {
  instance_arn = tolist(data.aws_ssoadmin_instances.sso.arns)[0]
  name         = "Admin"
}

data "aws_identitystore_group" "admin_identitystore_group" {
  identity_store_id = tolist(data.aws_ssoadmin_instances.sso.identity_store_ids)[0]
  filter {
    attribute_path  = "DisplayName"
    attribute_value = "Admin"
  }
}

resource "aws_ssoadmin_account_assignment" "admin_sso" {
  instance_arn       = data.aws_ssoadmin_permission_set.admin_permission_set.instance_arn
  permission_set_arn = data.aws_ssoadmin_permission_set.admin_permission_set.arn
  principal_id       = data.aws_identitystore_group.admin_identitystore_group.group_id
  principal_type     = "GROUP"
  target_id          = aws_organizations_account.member_account.id
  target_type        = "AWS_ACCOUNT"
}