resource "aws_iam_user" "member_account_user" {
  name     = var.iam_user
  provider = aws.member_account_provider
  path     = "/system/"
  tags     = var.tags
}

resource "aws_iam_access_key" "member_account_user_key" {
  user     = aws_iam_user.member_account_user.name
  pgp_key  = var.public_key
  provider = aws.member_account_provider
}

resource "aws_iam_policy" "s3_policy" {
  name        = "self-serve-s3-policy"
  description = "Policy for access related to s3"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.s3_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "s3_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.s3_policy.arn
  provider   = aws.member_account_provider
}

resource "aws_iam_policy" "iam_policy" {
  name        = "self-serve-iam-policy"
  description = "Policy for access related to iam"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.iam_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "iam_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.iam_policy.arn
  provider   = aws.member_account_provider
}
resource "aws_iam_policy" "eks_policy" {
  name        = "self-serve-eks-policy"
  description = "Policy for access related to eks"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.eks_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "eks_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.eks_policy.arn
  provider   = aws.member_account_provider
}

resource "aws_iam_policy" "db_policy" {
  name        = "self-serve-db-policy"
  description = "Policy for access related to DB"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.db_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "db_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.db_policy.arn
  provider   = aws.member_account_provider
}

resource "aws_iam_policy" "cdn_policy" {
  name        = "self-serve-cdn-policy"
  description = "Policy for access related to CDN"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.cdn_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "cdn_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.cdn_policy.arn
  provider   = aws.member_account_provider
}

resource "aws_iam_policy" "extra_policy" {
  name        = "self-serve-extra-policy"
  description = "Policy for access related to other extra services"
  provider    = aws.member_account_provider
  policy      = data.aws_iam_policy_document.extra_policy_document.json
  tags        = var.tags
}

resource "aws_iam_user_policy_attachment" "extra_policy_attachment" {
  user       = aws_iam_user.member_account_user.name
  policy_arn = aws_iam_policy.extra_policy.arn
  provider   = aws.member_account_provider
}