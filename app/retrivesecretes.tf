data "aws_secretsmanager_secret" "rds" {
  name = "rds_endpoint"
}

data "aws_secretsmanager_secret_version" "rds" {
  secret_id = data.aws_secretsmanager_secret.rds.id
}

data "aws_secretsmanager_secret" "redis" {
  name = "redis_endpoint"
}

data "aws_secretsmanager_secret_version" "redis" {
  secret_id = data.aws_secretsmanager_secret.redis.id
}

data "aws_secretsmanager_secret" "subnet_private" {
  name = "subnet_private"
}

data "aws_secretsmanager_secret_version" "subnet_private" {
  secret_id = data.aws_secretsmanager_secret.subnet_private.id
}

data "aws_secretsmanager_secret" "postgres_password" {
  name = "postgres_password"
}

data "aws_secretsmanager_secret_version" "postgres_password" {
  secret_id = data.aws_secretsmanager_secret.postgres_password.id
}

data "aws_secretsmanager_secret" "airflow_password" {
  name = "airflow_password"
}

data "aws_secretsmanager_secret_version" "airflow_password" {
  secret_id = data.aws_secretsmanager_secret.airflow_password.id
}

data "aws_secretsmanager_secret" "ui_domain" {
  name = "ui_domain"
}

data "aws_secretsmanager_secret_version" "ui_domain" {
  secret_id = data.aws_secretsmanager_secret.ui_domain.id
}

data "aws_secretsmanager_secret" "airbyte_domain" {
  name = "airbyte_domain"
}

data "aws_secretsmanager_secret_version" "airbyte_domain" {
  secret_id = data.aws_secretsmanager_secret.airbyte_domain.id
}

data "aws_secretsmanager_secret" "bi_domain" {
  name = "bi_domain"
}

data "aws_secretsmanager_secret_version" "bi_domain" {
  secret_id = data.aws_secretsmanager_secret.bi_domain.id
}

data "aws_secretsmanager_secret" "superset_password" {
  name = "superset_password"
}

data "aws_secretsmanager_secret_version" "superset_password" {
  secret_id = data.aws_secretsmanager_secret.superset_password.id
}