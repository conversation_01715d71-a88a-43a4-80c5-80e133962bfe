# # Redis Cluster deploy on Kubernetes. #

resource "helm_release" "redis" {
  name             = "redis"
  repository       = "https://charts.bitnami.com/bitnami"
  chart            = "redis"
  version          = "20.1.0"
  namespace        = "redis"
  create_namespace = true
  set {
    name  = "fullnameOverride"
    value = "redis"
  }

  set {
    name  = "nameOverride"
    value = "redis"
  }
  set {
    name  = "replica.replicaCount"
    value = "2"
  }
}

