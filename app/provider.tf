terraform {
  required_version = ">= 0.13"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.40.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "2.25.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "2.12.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "3.4.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "4.38.0"
    }
    # time = {
    #   source = "hashicorp/time"
    #   version = "0.7.2"
    # }
  }
}
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.eks_cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.eks_cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.eks_cluster.token
  }
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.eks_cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.eks_cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.eks_cluster.token
  # load_config_file       = false
}

provider "cloudflare" {
  api_token = var.apps_default_hosted_api_token
}


# Get AWS Account ID
data "aws_caller_identity" "current" {}

# Declare the data source
data "aws_availability_zones" "available" {}

provider "http" {}

data "aws_eks_cluster" "eks_cluster" {
  name = "${var.organization_name}_${var.environment}"
}

data "aws_eks_cluster_auth" "eks_cluster" {
  name = "${var.organization_name}_${var.environment}"
}
