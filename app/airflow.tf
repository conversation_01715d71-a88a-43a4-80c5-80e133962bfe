resource "kubernetes_namespace" "etl" {
  metadata {
    name = "etl"
  }
}
resource "kubernetes_config_map" "aws-configmap" {
  metadata {
    name      = "awskey"
    namespace = kubernetes_namespace.etl.id
  }

  data = {
    AWS_ACCESS_KEY_ID     = var.aws_access_key
    AWS_SECRET_ACCESS_KEY = var.aws_secret_key
    AWS_DEFAULT_REGION    = var.aws_region
  }
}
resource "kubernetes_config_map" "etl" {
  metadata {
    name      = "special-config"
    namespace = kubernetes_namespace.etl.id
  }
  data = {
    AWS_ACCESS_KEY_ID                                 = var.aws_access_key
    AWS_SECRET_ACCESS_KEY                             = var.aws_secret_key
    AWS_DEFAULT_REGION                                = var.aws_region
    AMAZON_ACCOUNT_ID                                 = data.aws_caller_identity.current.account_id
    GLUE_ROLE_NAME                                    = aws_iam_role.glue.name
    PRIVATE_SUBNET                                    = "${data.aws_secretsmanager_secret_version.subnet_private.secret_string}"
    EMR_EC2_KEY_PAIR                                  = "${var.organization_name}_${var.environment}"
    EMR_BOOTSTRAP_BUCKET                              = aws_s3_bucket.emr_bucket.bucket
    SCHEMA                                            = "${var.organization_name}_${var.environment}"
    PREDICTION_SCHEMA                                 = "${var.organization_name}_prediction_${var.environment}"
    DATABASE                                          = "awsdatacatalog"
    LANDING_BUCKET                                    = aws_s3_bucket.landing_bucket.bucket
    STAGING_BUCKET                                    = aws_s3_bucket.staging_bucket.bucket
    PROCESSED_BUCKET                                  = aws_s3_bucket.processed_bucket.bucket
    QUERY_LOGS_BUCKET                                 = aws_s3_bucket.athena_logs.bucket
    ORGANIZATION_NAME                                 = var.organization_name
    ENVIRONMENT                                       = var.environment
    AIRFLOW__SMTP__SMTP_HOST                          = var.smtp_host
    AIRFLOW__SMTP__SMTP_MAIL_FROM                     = var.smtp_mail_from
    AIRFLOW__SMTP__SMTP_PASSWORD                      = var.smtp_password
    AIRFLOW__SMTP__SMTP_PORT                          = var.smtp_port
    AIRFLOW__SMTP__SMTP_SSL                           = var.smtp_ssl
    AIRFLOW__SMTP__SMTP_USER                          = var.smtp_user
    AIRFLOW__SMTP__SMTP_STARTTLS                      = false
    AIRFLOW_USER_ID                                   = "${var.organization_name}@airflow.com"
    AIRFLOW_USER_PASSWORD                             = "${var.organization_name}-${data.aws_caller_identity.current.account_id}"
    AIRFLOW__API__AUTH_BACKENDS                       = "airflow.api.auth.backend.basic_auth"
    AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG           = 32
    AIRFLOW__CORE__DAG_CONCURRENCY                    = 16
    AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG            = 1
    AIRFLOW__CORE__DAGBAG_IMPORT_TIMEOUT              = 180
    AIRFLOW__WEBSERVER__WORKERS                       = 1
    AIRFLOW__WEBSERVER__BASE_URL                      = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}/airflow"
    AIRFLOW__CELERY__WORKER_CONCURRENCY               = 16
    AIRFLOW__WEBSERVER__LOG_FETCH_DELAY_SEC           = 30
    AIRFLOW__WEBSERVER__LOG_FETCH_TIMEOUT_SEC         = 120
    AIRFLOW__CELERY__OPERATION_TIMEOUT                = 30
    AIRFLOW_REDIS_HOST                                = "${data.aws_secretsmanager_secret_version.redis.secret_string}"
    AIRFLOW_REDIS_PORT                                = 6379
    AIRFLOW__WEBSERVER__ENABLE_PROXY_FIX              = true
    AIRFLOW__WEBSERVER__EXPOSE_CONFIG                 = true
    AIRFLOW__WEBSERVER__COOKIE_SECURE                 = true
    AIRFLOW__WEBSERVER__DEFAULT_WRAP                  = true
    AIRFLOW__LOGGING__CELERY_LOGGING_LEVEL            = "INFO"
    AIRFLOW__LOGGING__LOGGING_LEVEL                   = "INFO"
    AIRFLOW__LOGGING__DAG_PROCESSOR_MANAGER_LOG_LEVEL = "INFO"
    AIRFLOW__LOGGING__REMOTE_LOGGING                  = true
    AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER          = "s3://${aws_s3_bucket.emr_bucket.bucket}/airflow-logs"
    AIRFLOW__LOGGING__ENCRYPT_S3_LOGS                 = false
    AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID              = "aws_default"
    AIRFLOW__CONNECTIONS__AWS_DEFAULT                 = "aws://${var.aws_access_key}:${var.aws_secret_key}@?region=${var.aws_region}"
    AIRFLOW__CORE__DAG_FILE_PROCESSOR_TIMEOUT         = 600
    AIRFLOW__CORE__STORE_SERIALIZED_DAGS              = true
    REDIS_PIPELINE_KEY                                = "bo:pipeline:flag"
    GITHUB_TOKEN_VALUE                                = var.git_token
    GITHUB_HANDLE_NAME                                = var.github_handle_name

  }
}
resource "helm_release" "etl" {
  name      = "airflow"
  namespace = kubernetes_namespace.etl.id
  timeout   = "600"
  depends_on = [
    kubernetes_job.create_db,
    kubernetes_config_map.etl,
    kubernetes_job.events_cohorts
  ]
  repository = "https://charts.bitnami.com/bitnami"
  chart      = "airflow"
  version    = var.airflow["chart_version"]
  lifecycle {
    replace_triggered_by = [
      kubernetes_config_map.etl
    ]
  }

  set {
    name  = "scheduler.image.repository"
    value = var.airflow["scheduler"]["image"]["repository"]
    type  = "auto"
  }
  set {
    name  = "scheduler.image.tag"
    value = var.airflow["scheduler"]["image"]["version"]
    type  = "auto"
  }
  set {
    name  = "worker.image.repository"
    value = var.airflow["worker"]["image"]["repository"]
    type  = "auto"
  }
  set {
    name  = "worker.image.tag"
    value = var.airflow["worker"]["image"]["version"]
    type  = "auto"
  }
  set {
    name  = "web.image.repository"
    value = var.airflow["web"]["image"]["repository"]
    type  = "auto"
  }
  set {
    name  = "web.image.tag"
    value = var.airflow["web"]["image"]["version"]
    type  = "auto"
  }
  set {
    name  = "postgresql.enabled"
    value = "false"
  }
  set {
    name  = "externalDatabase.host"
    value = data.aws_secretsmanager_secret_version.rds.secret_string
  }
  set {
    name  = "externalDatabase.user"
    value = var.postgres_user
  }
  set {
    name  = "externalDatabase.password"
    value = data.aws_secretsmanager_secret_version.postgres_password.secret_string
  }
  set {
    name  = "externalDatabase.database"
    value = "airflow"
  }
  set {
    name  = "redis.auth.password"
    value = "S8K28mhFE6"
  }
  set {
    name  = "auth.username"
    value = var.airflow_username
  }
  set {
    name  = "auth.password"
    value = data.aws_secretsmanager_secret_version.airflow_password.secret_string
  }
  set {
    name  = "nameOverride"
    value = "airflow"
  }
  set {
    name  = "fullnameOverride"
    value = "airflow"
  }
  set {
    name  = "auth.fernetKey"
    value = "Z0Ruem1LNktJTjFyT3YxM3JveTJkU2FMRnFmTjZvWjg="
  }
  set {
    name  = "auth.secretKey"
    value = "Z3YMeUW5FmaBvLXH"
  }
  set {
    name  = "serviceAccount.create"
    value = "true"
  }
  set {
    name  = "serviceAccount.name"
    value = "etl"
  }
  set {
    name  = "web.automountServiceAccountToken"
    value = "true"
  }

  set {
    name  = "scheduler.automountServiceAccountToken"
    value = "true"
  }

  set {
    name  = "worker.automountServiceAccountToken"
    value = "true"
  }

  set {
    name  = "rbac.create"
    value = "true"
  }
  set {
    name  = "scheduler.extraEnvVarsCM"
    value = "special-config"
  }
  set {
    name  = "web.extraEnvVarsCM"
    value = "special-config"
  }
  set {
    name  = "scheduler.resources.requests.cpu"
    value = "1000m"
  }
  set {
    name  = "scheduler.resources.requests.memory"
    value = "2000Mi"
  }
  set {
    name  = "scheduler.podDisruptionBudget.enabled"
    value = "true"
  }
  set {
    name  = "scheduler.podDisruptionBudget.minAvailable"
    value = 1
  }
  set {
    name  = "scheduler.replicaCount"
    value = 3
  }
  set {
    name  = "web.podDisruptionBudget.enabled"
    value = "true"
  }
  set {
    name  = "web.podDisruptionBudget.minAvailable"
    value = "1"
  }
  set {
    name  = "web.resources.requests.cpu"
    value = "200m"
  }
  set {
    name  = "web.resources.requests.memory"
    value = "512Mi"
  }
  set {
    name  = "worker.autoscaling.enabled"
    value = "false"
  }
  set {
    name  = "worker.replicaCount"
    value = "2"
  }
  set {
    name  = "worker.extraEnvVarsCM"
    value = "special-config"
  }
  set {
    name  = "worker.resources.requests.cpu"
    value = "400m"
  }
  set {
    name  = "worker.resources.requests.memory"
    value = "600Mi"
  }
  set {
    name  = "worker.podDisruptionBudget.enabled"
    value = "true"
  }
  set {
    name  = "worker.podDisruptionBudget.minAvailable"
    value = "1"
  }
  set {
    name  = "web.baseUrl"
    value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}/airflow"
  }
  set {
    name  = "web.extraEnvVars[0].name"
    value = "AIRFLOW__CELERY__FLOWER_URL_PREFIX"
  }
  set {
    name  = "web.extraEnvVars[0].value"
    value = "/airflow"
  }
  set {
    name  = "web.extraEnvVars[1].name"
    value = "AIRFLOW_BASE_URL"
  }
  set {
    name  = "web.extraEnvVars[1].value"
    value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}/airflow"
  }
  set {
    name  = "global.imagePullSecrets[0]"
    value = "regcred"
  }
  set {
    name  = "scheduler.image.pullPolicy"
    value = "IfNotPresent"
  }
  set {
    name  = "auth.enabled"
    value = "true"
  }
  set {
    name  = "extraEnvVarsCM"
    value = "special-config"
  }
  # set {
  #   name  = "scheduler.image.pullSecrets[0]"
  #   value = "regcred"
  # }
  set {
    name  = "worker.image.pullPolicy"
    value = "IfNotPresent"
  }
  # set {
  #   name  = "worker.image.pullSecrets[0]"
  #   value = "regcred"
  # }
  set {
    name  = "web.image.pullPolicy"
    value = "IfNotPresent"
  }
  # set {
  #   name  = "web.image.pullSecrets[0]"
  #   value = "regcred"
  # }
  set {
    name  = "git.dags.enabled"
    value = true
  }
  set {
    name  = "git.dags.repositories[0].repository"
    value = "https://${var.git_user}:${var.git_token}@github.com/blotoutio/${var.airflow_repo_path}.git"
  }
  set {
    name  = "git.dags.repositories[0].name"
    value = var.airflow["dags"]["repository"]["name"]
  }
  set {
    name  = "git.dags.repositories[0].branch"
    value = var.airflow["dags"]["repository"]["branch"]
  }
  set {
    name  = "git.dags.repositories[0].path"
    value = var.airflow["dags"]["repository"]["path"]
  }
}


resource "kubernetes_ingress_v1" "etl_ingress" {
  metadata {
    name      = "airflow"
    namespace = kubernetes_namespace.etl.id
    annotations = {
      "nginx.ingress.kubernetes.io/rewrite-target" = "/airflow/$2"
      "nginx.ingress.kubernetes.io/use-regex"      = "true"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = data.aws_secretsmanager_secret_version.ui_domain.secret_string
      http {
        path {
          backend {
            service {
              name = "airflow"
              port {
                name = "http"
              }
            }
          }

          path = "/airflow(/|$)(.*)"
        }
      }
    }
  }
}