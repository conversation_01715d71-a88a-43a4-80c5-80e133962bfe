# backend_api deploy on Kubernetes. #

data "kubernetes_secret" "airbyte_auth_secrets" {
  depends_on = [helm_release.airbyte_v1]
  metadata {
    name      = "airbyte-auth-secrets"
    namespace = "airbyte"
  }
}

resource "kubernetes_config_map" "api-configmap" {
  metadata {
    name = "api-configmap"
  }

  data = {
    SERVER_PORT             = "8080"
    LOGGING_FILE            = "/tmp/access.log"
    AWS_ACCESS_KEY_ID       = "${var.aws_access_key}"
    AWS_SECRET_ACCESS_KEY   = "${var.aws_secret_key}"
    AWS_DEFAULT_REGION      = "${var.aws_region}"
    AMAZON_ACCOUNT_ID       = "${data.aws_caller_identity.current.account_id}"
    ORGANIZATION_ID         = "${var.organization_name}"
    ENV                     = "${var.environment}"
    LANDING_BUCKET          = "${aws_s3_bucket.landing_bucket.bucket}"
    STAGING_BUCKET          = "${aws_s3_bucket.staging_bucket.bucket}"
    PROCESSED_BUCKET        = "${aws_s3_bucket.processed_bucket.bucket}"
    POSTGRESS_ENDPOINT      = "${data.aws_secretsmanager_secret_version.rds.secret_string}"
    POSTGRESS_PORT          = "5432"
    POSTGRESS_DATABASE_NAME = "blotout"
    POSTGRESS_USER          = "${var.postgres_user}"
    POSTGRES_PASSWORD       = "${data.aws_secretsmanager_secret_version.postgres_password.secret_string}"
    REDIS_CLUSTER_NODES     = "${data.aws_secretsmanager_secret_version.redis.secret_string}:6379"
    GOOGLE_CLIENT_ID        = "${var.google_client_id}"
    GOOGLE_CLIENT_SECRET    = "${var.google_client_secret}"
    MS_CLIENT_SECRET        = "${var.ms_client_secret}"
    MS_CLIENT_ID            = "${var.ms_client_id}"
    SMTP_HOST               = "${var.smtp_host}"
    SMTP_PORT               = "${var.smtp_port}"
    SMTP_USER               = "${var.smtp_user}"
    SMTP_PASSWORD           = "${var.smtp_password}"
    SMTP_SSL                = "${var.smtp_ssl}"
    SMTP_SOCKETFACTORY_PORT = "${var.smtp_socketfactory_port}"
    SMTP_MAIL_FROM          = "${var.smtp_mail_from}"
    ATHENA_DATABASE         = "${var.organization_name}_${var.environment}"
    ATHENA_OUTPUT_BUCKET    = "s3://${aws_s3_bucket.athena_logs.bucket}"
    ATHENA_ACCESS_KEY_ID    = "${var.aws_access_key}"
    ATHENA_SECRET_ACCES_KEY = "${var.aws_secret_key}"
    ATHENA_REGION           = "${var.aws_region}"
    AIRFLOW_USER_ID         = "${var.organization_name}@airflow.com"
    AIRFLOW_USER_PASSWORD   = "${var.organization_name}-${data.aws_caller_identity.current.account_id}"
    AIRBYTE_CLIENT_ID       = data.kubernetes_secret.airbyte_auth_secrets.data["instance-admin-client-id"]
    AIRBYTE_CLIENT_SECRET   = data.kubernetes_secret.airbyte_auth_secrets.data["instance-admin-client-secret"]

    "airbyte.profile.columns"           = "id,first_name,last_name,middle_name,user_name,dob,email,mobile_number,address,city,state,zip,country,gender,age"
    "airbyte.resources.directory"       = "metadata"
    "airbyte.hostName"                  = "${data.aws_secretsmanager_secret_version.airbyte_domain.secret_string}"
    "airbyte.baseURL"                   = "https://${data.aws_secretsmanager_secret_version.airbyte_domain.secret_string}"
    "airbyte.workspace_id"              = "5ae6b09b-fdec-41af-aaf7-7d94cfc33ef6"
    "airbyte.access_key_id"             = "${var.aws_access_key}"
    "airbyte.secret_access_key"         = "${var.aws_secret_key}"
    "airbyte.s3_bucket_region"          = "${var.aws_region}"
    "airbyte.s3_bucket_name"            = "b-${var.organization_name}-${var.environment}-stg"
    "airbyte.destination_definition_id" = "4816b78f-1489-44c1-9060-4b19d5fa9362"

    GIT_HOSTNAME = "raw.githubusercontent.com"
    GIT_PATH     = "blotoutio/elt-source-mapping/main/{SOURCE_NAME}.json"
  }
}

resource "helm_release" "backend_api" {
  name = "backend-api"
  depends_on = [
    kubernetes_config_map.api-configmap,
    helm_release.ingress,
    helm_release.etl
  ]
  repository = "https://blotoutio.github.io/charts"
  chart      = "dashboard-backend"
  version    = var.dashboard_backend["chart_version"]
  namespace  = "default"
  lifecycle {
    replace_triggered_by = [
      # Replace `aws_appautoscaling_target` each time this instance of
      # the `aws_ecs_service` is replaced.
      kubernetes_config_map.api-configmap
    ]
  }

  set {
    name  = "image.repository"
    value = var.dashboard_backend["image"]["repository"]
  }
  set {
    name  = "image.tag"
    value = var.dashboard_backend["image"]["version"]
  }
  set {
    name  = "autoscaling.enabled"
    value = "true"
    type  = "auto"
  }

  set {
    name  = "autoscaling.targetCPUUtilizationPercentage"
    value = 50
  }

  set {
    name  = "autoscaling.targetMemoryUtilizationPercentage"
    value = 60
  }

  set {
    name  = "autoscaling.maxReplicas"
    value = "2"
  }

  set {
    name  = "env.API_BASE_URL"
    value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}"
  }

  set {
    name  = "env.SUPERSET_BASE_URL"
    value = "https://${data.aws_secretsmanager_secret_version.bi_domain.secret_string}"
  }

  set {
    name  = "env.USER_REPORTING_LIMIT"
    value = var.user_reporting_limit
  }

  set {
    name  = "env.PREDICTION_SCHEMA"
    value = "${var.organization_name}_prediction_${var.environment}"
  }

  set {
    name  = "ingress.enabled"
    value = true
  }

  set {
    name  = "ingress.hosts[0].host"
    value = data.aws_secretsmanager_secret_version.ui_domain.secret_string
  }

  set {
    name  = "env.SUPERSET_HOSTNAME"
    value = data.aws_secretsmanager_secret_version.bi_domain.secret_string
  }

  set {
    name  = "ingress.hosts[0].paths[0].path"
    value = "/api"
  }

  set {
    name  = "ingress.hosts[0].paths[0].pathType"
    value = "ImplementationSpecific"
  }

  set {
    name  = "livenessProbe.httpGet.path"
    value = "/api/v1/health"
  }

  set {
    name  = "livenessProbe.httpGet.port"
    value = 8080
  }

  set {
    name  = "nameOverride"
    value = "blotoutapi"
  }

  set {
    name  = "fullnameOverride"
    value = "blotoutapi"
  }

  set {
    name  = "readinessProbe.httpGet.path"
    value = "/api/v1/health"
  }

  set {
    name  = "readinessProbe.httpGet.port"
    value = 8080
  }

  set {
    name  = "service.port"
    value = 8080
  }

  set {
    name  = "imagePullSecrets[0].name"
    value = "regcred"
  }

  set {
    name  = "env.LOGGING_LEVEL_COM_BLOT"
    value = var.logging_level_com_blot
  }

  set {
    name  = "env.BLOTOUT_LOGIN_URL"
    value = data.aws_secretsmanager_secret_version.ui_domain.secret_string
  }

  set {
    name  = "env.FIREHOSE_ROLE_NAME"
    value = aws_iam_role.firehose.name
  }

  set {
    name  = "env.REDIS_QUERY_TTL"
    value = 900
  }

  set {
    name  = "env.APPLICATION_S3_FOLDER"
    value = "clickstream"
  }

  set {
    name  = "env.CRYPTOKEY"
    value = var.cryptokey
  }

  set {
    name  = "env.TOKEN_EXPIRY"
    value = var.token_expiry
  }

  set {
    name  = "env.USERPASSWORD_EXPIRY"
    value = var.userpassword_expiry
  }

  set {
    name  = "env.ATHENA_TIMEOUT"
    value = 100000
  }

  set {
    name  = "resources.limits.cpu"
    value = "1000m"
  }

  set {
    name  = "resources.limits.memory"
    value = "4Gi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "250m"
  }

  set {
    name  = "resources.requests.memory"
    value = "600Mi"
  }

  set {
    name  = "swagger.enabled"
    value = true
  }
}
