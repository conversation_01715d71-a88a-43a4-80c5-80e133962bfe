resource "kubernetes_job" "events_cohorts" {

  count = var.run_events_cohorts == true ? 1 : 0
  metadata {
    name      = "events-cohorts"
    namespace = "default"
  }
  spec {
    template {
      metadata {}
      spec {
        image_pull_secrets {
          name = "regcred"
        }
        container {
          name              = "events-cohorts"
          image             = "${var.infra_script["image"]["repository"]}:${var.infra_script["image"]["version"]}"
          image_pull_policy = "IfNotPresent"
          command           = ["python3", "tags.py"]
          env {
            name  = "AWS_ACCESS_KEY_ID"
            value = var.aws_access_key
          }
          env {
            name  = "AWS_SECRET_ACCESS_KEY"
            value = var.aws_secret_key
          }
          env {
            name  = "AWS_DEFAULT_REGION"
            value = var.aws_region
          }
          env {
            name  = "ORGANIZATION_NAME"
            value = var.organization_name
          }
          env {
            name  = "ENV"
            value = var.environment
          }
        }
        restart_policy = "Never"
      }
    }
    backoff_limit = 4
  }
  wait_for_completion = false
}
