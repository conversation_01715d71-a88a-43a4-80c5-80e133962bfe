data "aws_vpc" "vpc" {
  filter {
    name   = "tag:Name"
    values = ["${var.organization_name}_${var.environment}"]
  }
  filter {
    name   = "tag:Cluster-name"
    values = [var.organization_name]
  }
  filter {
    name   = "tag:Environment"
    values = [var.environment]
  }
  filter {
    name   = "tag:kubernetes.io/cluster/${var.organization_name}_${var.environment}"
    values = ["shared"]
  }
}