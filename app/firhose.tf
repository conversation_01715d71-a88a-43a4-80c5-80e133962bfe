resource "aws_iam_role" "firehose" {
  name               = "firehose_role_${var.organization_name}_${var.environment}"
  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": "firehose.amazonaws.com"
      },
      "Action": "sts:AssumeRole",
      "Condition": {
        "StringEquals": {
          "sts:ExternalId": "${data.aws_caller_identity.current.account_id}"
        }
      }
    }
  ]
}
POLICY


  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "kinesis_firehose"
    Description  = "IAM Role for Kenisis Firehose"
  }
}

resource "aws_iam_policy" "firehose_policy" {
  name        = "firehose_policy_${var.organization_name}_${var.environment}"
  description = "firhose policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid      = "VisualEditor0"
        Effect   = "Allow"
        Action   = ["firehose:ListDeliveryStreams", ]
        Resource = "*"
      },
      {
        Sid      = "VisualEditor1"
        Effect   = "Allow"
        Action   = ["firehose:*", ]
        Resource = "arn:aws:firehose:*:${data.aws_caller_identity.current.account_id}:deliverystream/${aws_s3_bucket.landing_bucket.id}"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject",
        ]
        Resource = [
          "arn:aws:s3:::${aws_s3_bucket.landing_bucket.id}",
          "arn:aws:s3:::${aws_s3_bucket.landing_bucket.id}/*",
          "arn:aws:s3:::${aws_s3_bucket.processed_bucket.id}",
          "arn:aws:s3:::${aws_s3_bucket.processed_bucket.id}/*"
        ]
      },
    ]
  })
}


resource "aws_iam_role_policy_attachment" "firehose_role_attachment" {
  role       = aws_iam_role.firehose.name
  policy_arn = aws_iam_policy.firehose_policy.arn
}