# Ensure records are created properly
resource "cloudflare_record" "ui-endpoint" {
  allow_overwrite = true
  zone_id         = var.apps_default_hosted_zone_id
  name            = local.ui_domain
  value           = data.aws_lb.lb.dns_name
  type            = "CNAME"
  ttl             = 1
  proxied         = true
}

resource "cloudflare_record" "airbyte-endpoint" {
  allow_overwrite = true
  zone_id         = var.apps_default_hosted_zone_id
  name            = local.airbyte_domain
  value           = data.aws_lb.lb.dns_name
  type            = "CNAME"
  ttl             = 1
  proxied         = true
}

resource "cloudflare_record" "bi-endpoint" {
  allow_overwrite = "true"
  zone_id         = var.apps_default_hosted_zone_id
  name            = local.superset_domain
  value           = data.aws_lb.lb.dns_name
  type            = "CNAME"
  ttl             = "1"
  proxied         = true
}
# Create ACM certificate for ui_domain
resource "aws_acm_certificate" "aws_acm_cert" {
  domain_name       = local.ui_domain
  validation_method = "DNS"

  tags = {
    Name        = var.organization_name
    owner       = var.organization_name
    terraform   = "true"
    Environment = var.environment
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Add DNS validation records for the ACM certificate (ui_domain)
resource "cloudflare_record" "endpoints" {
  for_each = {
    for dvo in aws_acm_certificate.aws_acm_cert.domain_validation_options : dvo.domain_name => {
      name  = dvo.resource_record_name
      value = dvo.resource_record_value
      type  = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  value           = each.value.value
  ttl             = 60
  type            = each.value.type
  zone_id         = var.apps_default_hosted_zone_id
}

# Validate ACM certificate for ui_domain
resource "aws_acm_certificate_validation" "validation" {
  certificate_arn         = aws_acm_certificate.aws_acm_cert.arn
  validation_record_fqdns = [for value in cloudflare_record.endpoints : value.hostname]

  timeouts {
    create = "10m"
  }
}

# Create ACM certificate for airbyte_domain
resource "aws_acm_certificate" "airbyte_aws_acm_cert" {
  domain_name       = local.airbyte_domain
  validation_method = "DNS"

  tags = {
    Name        = var.organization_name
    owner       = var.organization_name
    terraform   = "true"
    Environment = var.environment
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Add DNS validation records for the ACM certificate (airbyte_domain)
resource "cloudflare_record" "airbyte_endpoints" {
  for_each = {
    for dvo in aws_acm_certificate.airbyte_aws_acm_cert.domain_validation_options : dvo.domain_name => {
      name  = dvo.resource_record_name
      value = dvo.resource_record_value
      type  = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  value           = each.value.value
  ttl             = 60
  type            = each.value.type
  zone_id         = var.apps_default_hosted_zone_id
}

# Validate ACM certificate for airbyte_domain
resource "aws_acm_certificate_validation" "airbyte_validation" {
  certificate_arn         = aws_acm_certificate.airbyte_aws_acm_cert.arn
  validation_record_fqdns = [for value in cloudflare_record.airbyte_endpoints : value.hostname]

  timeouts {
    create = "10m"
  }
}

# Data source for AWS Load Balancer for ui_domain
data "aws_lb" "lb" {
  depends_on = [helm_release.ingress]
  tags = {
    "kubernetes.io/service-name"                                        = "ingress-nginx/nginx-ingress-controller"
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "owned"
  }
}

# Data source for AWS Load Balancer Listener for ui_domain
data "aws_lb_listener" "lb_listener" {
  load_balancer_arn = data.aws_lb.lb.arn
  port              = 443
}

# Attach the certificate to the listener for ui_domain
resource "aws_lb_listener_certificate" "add_lb_listener_cert" {
  listener_arn    = data.aws_lb_listener.lb_listener.arn
  certificate_arn = aws_acm_certificate.aws_acm_cert.arn
}

# Data source for AWS Load Balancer for airbyte_domain
data "aws_lb" "airbyte_lb" {
  depends_on = [helm_release.ingress]
  tags = {
    "kubernetes.io/service-name"                                        = "ingress-nginx/nginx-ingress-controller"
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "owned"
  }
}

# Data source for AWS Load Balancer Listener for airbyte_domain
data "aws_lb_listener" "airbyte_lb_listener" {
  load_balancer_arn = data.aws_lb.airbyte_lb.arn
  port              = 443
}

# Attach the certificate to the listener for airbyte_domain
resource "aws_lb_listener_certificate" "airbyte_add_lb_listener_cert" {
  listener_arn    = data.aws_lb_listener.airbyte_lb_listener.arn
  certificate_arn = aws_acm_certificate.airbyte_aws_acm_cert.arn

  depends_on = [aws_acm_certificate_validation.airbyte_validation]
}
