resource "aws_kinesis_firehose_delivery_stream" "activation_channel_status" {
  name        = "activation_channel_status_${var.environment}"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose.arn
    bucket_arn         = aws_s3_bucket.landing_bucket.arn
    buffering_interval = "60"
    buffering_size     = "5"

    cloudwatch_logging_options {
      enabled = "false"
    }

    compression_format  = "UNCOMPRESSED"
    error_output_prefix = "activation/errors/"
    prefix              = "activation/activation_channel_status/event_date=!{timestamp:yyyy-MM-dd}/"
    s3_backup_mode      = "Disabled"
  }

  server_side_encryption {
    enabled  = "false"
    key_type = "AWS_OWNED_CMK"
  }
}

resource "aws_kinesis_firehose_delivery_stream" "activation" {
  name        = "activation_${var.environment}"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose.arn
    bucket_arn         = aws_s3_bucket.landing_bucket.arn
    buffering_interval = "60"
    buffering_size     = "5"

    cloudwatch_logging_options {
      enabled = "false"
    }

    compression_format  = "UNCOMPRESSED"
    error_output_prefix = "activation/errors/"
    prefix              = "activation/activation/event_date=!{timestamp:yyyy-MM-dd}/"
    s3_backup_mode      = "Disabled"
  }

  server_side_encryption {
    enabled  = "false"
    key_type = "AWS_OWNED_CMK"
  }
}

resource "aws_kinesis_firehose_delivery_stream" "stream" {
  count       = var.firehose_count
  name        = "${var.organization_name}_${var.environment}_${count.index + 1}"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose.arn
    bucket_arn         = aws_s3_bucket.processed_bucket.arn
    buffering_interval = "600"
    buffering_size     = "20"

    cloudwatch_logging_options {
      enabled = "false"
    }

    compression_format  = "UNCOMPRESSED"
    error_output_prefix = "${var.organization_name}_${var.environment}/errors/"
    prefix              = "${var.organization_name}_${var.environment}/events/event_date=!{timestamp:yyyy-MM-dd}/"
    s3_backup_mode      = "Disabled"
  }

  server_side_encryption {
    enabled  = "false"
    key_type = "AWS_OWNED_CMK"
  }

}

resource "aws_kinesis_firehose_delivery_stream" "error" {
  name        = "${var.organization_name}_${var.environment}_errors"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose.arn
    bucket_arn         = aws_s3_bucket.processed_bucket.arn
    buffering_interval = "600"
    buffering_size     = "30"

    cloudwatch_logging_options {
      enabled         = "true"
      log_group_name  = aws_cloudwatch_log_group.error.name
      log_stream_name = "DestinationDelivery"
    }

    compression_format  = "UNCOMPRESSED"
    error_output_prefix = "${var.organization_name}_${var.environment}_errors/errors/"
    prefix              = "${var.organization_name}_${var.environment}_errors/events/event_date=!{timestamp:yyyy-MM-dd}/"
    s3_backup_mode      = "Disabled"
  }

  server_side_encryption {
    enabled  = "false"
    key_type = "AWS_OWNED_CMK"
  }
}

resource "aws_cloudwatch_log_group" "error" {
  name              = "/aws/kinesisfirehose/${var.organization_name}_${var.environment}_errors"
  retention_in_days = 0
}

resource "aws_cloudwatch_log_stream" "error" {
  count          = length(var.log_stream_names)
  name           = var.log_stream_names[count.index]
  log_group_name = aws_cloudwatch_log_group.error.name
}