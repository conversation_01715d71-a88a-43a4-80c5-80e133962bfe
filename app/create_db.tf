resource "kubernetes_job" "create_db" {
  metadata {
    name      = "create-db"
    namespace = "default"
  }
  spec {
    template {
      metadata {}
      spec {
        image_pull_secrets {
          name = "regcred"
        }
        container {
          name              = "create-db"
          image             = "${var.infra_script["image"]["repository"]}:${var.infra_script["image"]["version"]}"
          image_pull_policy = "IfNotPresent"
          command           = ["python3", "create.py"]
          env {
            name  = "POSTGRES_ENDPOINT"
            value = data.aws_secretsmanager_secret_version.rds.secret_string
          }
          env {
            name  = "POSTGRES_USER"
            value = var.postgres_user
          }
          env {
            name  = "POSTGRES_PASSWORD"
            value = data.aws_secretsmanager_secret_version.postgres_password.secret_string
          }
          env {
            name  = "POSTGRES_DATABASE"
            value = var.postgres_database
          }
          env {
            name  = "POSTGRES_CREATE_DATABASE"
            value = var.required_databases
          }
        }
        restart_policy = "Never"
      }
    }
    backoff_limit = 4
  }
  wait_for_completion = false
}
