resource "kubernetes_namespace" "airbyte" {
  metadata {
    name = "airbyte"
  }

}

resource "helm_release" "airbyte_v1" {
  depends_on = [
    helm_release.ingress,
  ]
  name       = "airbyte-v1"
  namespace  = kubernetes_namespace.airbyte.metadata[0].name
  repository = "https://blotoutio.github.io/charts"
  chart      = "airbyte-v1"
  version    = var.airbyte_v1["chart_version"]
  timeout    = 900

  set_sensitive {
    name  = "global.airbyteUrl"
    value = "https://${data.aws_secretsmanager_secret_version.airbyte_domain.secret_string}"
  }

  set {
    name  = "airbyte-bootloader.image.repository"
    value = var.airbyte_v1["bootloader"]["image"]["repository"]
  }
  set_sensitive {
    name  = "airbyte-bootloader.image.tag"
    value = var.airbyte_v1["bootloader"]["image"]["version"]
  }
  set {
    name  = "minio.image.repository"
    value = var.airbyte_v1["minio"]["image"]["repository"]
  }
  set {
    name  = "minio.image.tag"
    value = var.airbyte_v1["minio"]["image"]["version"]
  }
  set {
    name  = "minio.mcImage.repository"
    value = var.airbyte_v1["minio"]["image"]["repository"]
  }
  set {
    name  = "minio.mcImage.tag"
    value = var.airbyte_v1["minio"]["image"]["version"]
  }
  set_sensitive {
    name  = "cron.image.repository"
    value = var.airbyte_v1["cron"]["image"]["repository"]
  }
  set_sensitive {
    name  = "cron.image.tag"
    value = var.airbyte_v1["cron"]["image"]["version"]
  }
  set {
    name  = "workload-launcher.image.repository"
    value = var.airbyte_v1["workload-launcher"]["image"]["repository"]
  }
  set {
    name  = "workload-launcher.image.tag"
    value = var.airbyte_v1["workload-launcher"]["image"]["version"]
  }
  set_sensitive {
    name  = "container-orchestrator.image.repository"
    value = var.airbyte_v1["container-orchestrator"]["image"]["repository"]
  }
  set_sensitive {
    name  = "container-orchestrator.image.tag"
    value = var.airbyte_v1["container-orchestrator"]["image"]["version"]
  }
  set {
    name  = "workload-init-container.image.repository"
    value = var.airbyte_v1["workload-init-container"]["image"]["repository"]
  }
  set {
    name  = "workload-init-container.image.tag"
    value = var.airbyte_v1["workload-init-container"]["image"]["version"]
  }
  set_sensitive {
    name  = "connector-builder-server.image.repository"
    value = var.airbyte_v1["connector-builder-server"]["image"]["repository"]
  }
  set_sensitive {
    name  = "connector-builder-server.image.tag"
    value = var.airbyte_v1["connector-builder-server"]["image"]["version"]
  }
  set {
    name  = "workload-api-server.image.repository"
    value = var.airbyte_v1["workload-api-server"]["image"]["repository"]
  }
  set {
    name  = "workload-api-server.image.tag"
    value = var.airbyte_v1["workload-api-server"]["image"]["version"]
  }
  set {
    name  = "metrics.image.repository"
    value = var.airbyte_v1["metrics"]["image"]["repository"]
  }
  set {
    name  = "metrics.image.tag"
    value = var.airbyte_v1["metrics"]["image"]["version"]
  }
  set {
    name  = "pod-sweeper.image.repository"
    value = var.airbyte_v1["pod-sweeper"]["image"]["repository"]
  }
  set {
    name  = "pod-sweeper.image.tag"
    value = var.airbyte_v1["pod-sweeper"]["image"]["version"]
  }
  set {
    name  = "server.image.repository"
    value = var.airbyte_v1["server"]["image"]["repository"]
  }
  set {
    name  = "server.image.tag"
    value = var.airbyte_v1["server"]["image"]["version"]
  }
  set {
    name  = "webapp.image.repository"
    value = var.airbyte_v1["webapp"]["image"]["repository"]
  }
  set {
    name  = "webapp.image.tag"
    value = var.airbyte_v1["webapp"]["image"]["version"]
  }
  set {
    name  = "worker.image.repository"
    value = var.airbyte_v1["worker"]["image"]["repository"]
  }
  set {
    name  = "worker.image.tag"
    value = var.airbyte_v1["worker"]["image"]["version"]
  }
  set {
    name  = "nameOverride"
    value = "airbyte-v1"
  }

  set {
    name  = "webapp.ingress.enabled"
    value = true
  }

  set {
    name  = "webapp.ingress.hosts[0].host"
    value = data.aws_secretsmanager_secret_version.airbyte_domain.secret_string
  }

  set {
    name  = "webapp.ingress.hosts[0].paths[0].path"
    value = "/"
  }

  set {
    name  = "webapp.ingress.hosts[0].paths[0].pathType"
    value = "ImplementationSpecific"
  }

  set {
    name  = "webapp.api.url"
    value = "/api/v1"
  }

  set {
    name  = "global.imagePullSecrets[0].name"
    value = "regcred"
  }

  set {
    name  = "webapp.replicaCount"
    value = "2"
  }

  set {
    name  = "server.replicaCount"
    value = "3"
  }

  set {
    name  = "worker.replicaCount"
    value = "3"
  }

  set_sensitive {
    name  = "global.database.host"
    value = data.aws_secretsmanager_secret_version.rds.secret_string
  }

  set {
    name  = "global.database.port"
    value = "5432"
  }

  set {
    name  = "global.database.database"
    value = "airbyte"
  }

  set_sensitive {
    name  = "global.database.user"
    value = var.postgres_user
  }

  set_sensitive {
    name  = "global.database.password"
    value = data.aws_secretsmanager_secret_version.postgres_password.secret_string
  }
  set_sensitive {
    name  = "global.storage.bucket.log"
    value = aws_s3_bucket.airbyte_logs.bucket
  }
  set_sensitive {
    name  = "global.storage.bucket.state"
    value = aws_s3_bucket.airbyte_logs.bucket
  }
  set_sensitive {
    name  = "global.storage.bucket.workloadOutput"
    value = aws_s3_bucket.airbyte_logs.bucket
  }
  set_sensitive {
    name  = "global.storage.s3.accessKeyIdSecretKey"
    value = "AWS_ACCESS_KEY_ID"
  }

  set_sensitive {
    name  = "global.storage.s3.secretAccessKeySecretKey"
    value = "AWS_SECRET_ACCESS_KEY"
  }
  set_sensitive {
    name  = "global.storage.s3.accessKeyId"
    value = var.aws_access_key
  }

  set_sensitive {
    name  = "global.storage.s3.secretAccessKey"
    value = var.aws_secret_key
  }
  set_sensitive {
    name  = "global.aws_access_key"
    value = var.aws_access_key
  }
  set_sensitive {
    name  = "global.aws_secret_key"
    value = var.aws_secret_key
  }
  set {
    name  = "server.env_vars.BLOTOUT_AUTH_ENDPOINT"
    value = "/api/v1/auth/validation"
  }
  set {
    name  = "server.env_vars.BLOTOUT_BASE_URL"
    value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}"
  }
  lifecycle {
    ignore_changes = [metadata]
  }
}


resource "kubernetes_namespace" "nfs" {
  metadata {
    name = "nfs"
  }
}

resource "helm_release" "nfs" {
  name       = "nfs"
  repository = "https://kubernetes-sigs.github.io/nfs-ganesha-server-and-external-provisioner"
  chart      = "nfs-server-provisioner"
  version    = "1.8.0"
  namespace  = kubernetes_namespace.nfs.id
  set {
    name  = "persistence.enabled"
    value = true
  }
  set {
    name  = "persistence.size"
    value = var.nfs_disk_size
  }
  set {
    name  = "persistence.storageClass"
    value = "gp2"
  }
  set {
    name  = "storageClass.create"
    value = true
  }
  set {
    name  = "storageClass.defaultClass"
    value = true
  }
  set {
    name  = "storageClass.reclaimPolicy"
    value = "Retain"
  }

}
