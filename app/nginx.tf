resource "kubernetes_config_map_v1" "config-map" {
  metadata {
    name      = "config-map"
    namespace = kubernetes_namespace_v1.lineage.id
  }

  data = {
    "default.conf" = "${file("${path.module}/data/default.conf")}"
  }
}

resource "kubernetes_deployment_v1" "nginx" {
  metadata {
    name      = "nginx"
    namespace = kubernetes_namespace_v1.lineage.id
    labels = {
      app = "nginx"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        app = "nginx"
      }
    }

    template {
      metadata {
        labels = {
          app = "nginx"
        }
      }

      spec {
        container {
          image = "nginx:1.22-alpine"
          name  = "nginx"

          volume_mount {
            mount_path = "/etc/nginx/conf.d"
            name       = "config"
          }
        }
        volume {
          name = "config"
          config_map {
            name     = "config-map"
            optional = true
          }
        }
      }
    }
  }
}

resource "kubernetes_service_v1" "example" {
  metadata {
    name      = "nginx-svc"
    namespace = kubernetes_namespace_v1.lineage.id
  }
  spec {
    selector = {
      app = "nginx"
    }
    session_affinity = "ClientIP"
    port {
      port        = 80
      target_port = 80
    }

    type = "ClusterIP"
  }
}

resource "kubernetes_ingress_v1" "ingress" {
  metadata {
    name      = "nginx-ingress"
    namespace = kubernetes_namespace_v1.lineage.id
  }

  spec {
    default_backend {
      service {
        name = "nginx-svc"
        port {
          number = 80
        }
      }
    }

    rule {
      host = local.ui_domain
      http {
        path {
          backend {
            service {
              name = "nginx-svc"
              port {
                number = 80
              }
            }
          }

          path = "/data-lineage/*"
        }
      }
    }
  }
}