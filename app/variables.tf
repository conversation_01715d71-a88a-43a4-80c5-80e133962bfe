# EKS Cluster
variable "aws_region" {
  default = ""
}

variable "aws_access_key" {
  default   = ""
  sensitive = true
}

variable "aws_secret_key" {
  default   = ""
  sensitive = true
}

variable "organization_name" {
  default = ""
}

variable "environment" {
  default     = ""
  description = "define your environment ex. dev or prod"
}


# <PERSON>Flare 
variable "apps_default_hosted_zone_id" {
  default     = ""
  description = ""
}

variable "apps_default_hosted_api_token" {
  default     = ""
  description = ""
}

# Docker HUB 
variable "registry_server" {
  default = "https://index.docker.io/v1/"
}

variable "docker_username" {
  default = ""
}

variable "docker_password" {
  default = ""
}

# RDS
variable "postgres_user" {
  default = "rds_admin"
}

variable "postgres_database" {
  default = "blotout"
}

variable "required_databases" {
  default = "airbyte,airflow,superset"
}

## App Setting
variable "google_client_id" {
  default = "null"
}

variable "google_client_secret" {
  default = "null"
}

variable "ms_client_secret" {
  default = "null"
}

variable "ms_client_id" {
  default = "null"
}

variable "smtp_host" {
  default = "smtp.gmail.com"
}

variable "smtp_port" {
  default = "465"
}

variable "smtp_user" {
  default = ""
}

variable "smtp_password" {
  default = ""
}

variable "smtp_ssl" {
  default = "true"
}

variable "smtp_socketfactory_port" {
  default = "465"
}

variable "smtp_mail_from" {
  default = "<EMAIL>"
}

variable "logging_level_com_blot" {
  default = "DEBUG"
}

variable "cryptokey" {
  default = "blotout"
}

variable "token_expiry" {
  default = 604800
}

variable "userpassword_expiry" {
  default = 86400
}
variable "user_reporting_limit" {
  default = 200
}


variable "default_admin" {
  default = "ALL"
}

variable "guniocorn_workers" {
  default = "1"
}

variable "guniocorn_threads" {
  default = "20"
}

variable "guniocorn_timeout" {
  default = "60"
}

variable "celery_workers" {
  default = "2"
}

variable "celery_worker_child" {
  default = "128"
}

variable "slack_api_token" {
  default = "clientslackttoken"
}

#Airflow
variable "airflow_username" {
  default = "admin"
}

variable "airflow_job_start_date" {
  default = ""
}

variable "airflow_repo_path" {
  default = "airflow-dags"
}

# Github
variable "git_user" {
  default = ""
}

variable "git_token" {
  default = ""
}

variable "github_handle_name" {
  default = "blotoutio"
}

variable "failure_report_email" {
  default = "<EMAIL>"
}

#Airbyte
variable "nfs_disk_size" {
  default = "20Gi"
}

# Client
variable "administrator_email" {
  default = "null"
}

# Events cohorts job
variable "run_events_cohorts" {
  default = false
}

# firehose count
variable "firehose_count" {
  default = "1"
}

variable "log_stream_names" {
  type    = list(string)
  default = ["BackupDelivery", "DestinationDelivery"]
}

variable "superset_admin" {
  default = "admin"
}

#ShopGpt
variable "OTEL_EXPORTER_OTLP_PROTOCOL" {
  default = ""
}

variable "OTEL_EXPORTER_OTLP_ENDPOINT" {
  default = ""
}

variable "OTEL_API_KEY" {
  default = ""
}

variable "OPENAI_API_KEY" {
  default = ""
}

variable "PINECONE_API_KEY" {
  default = ""
}

variable "LANGFUSE_PUBLIC_KEY" {
  default = ""
}

variable "LANGFUSE_SECRET_KEY" {
  default = ""
}

variable "GROQ_API_KEY" {
  default = ""
}

variable "HONEYCOMB_LOGS_ENDPOINT" {
  default = ""
}

variable "LLM_API_KEY" {
  default = ""
}

# variable "shogpt_postgres_user" {
#   default = "postgres"
# }

# variable "shogpt_postgres_password" {
#   default = ""
# }

# variable "shogpt_rds_url" {
#   default = ""
# }

