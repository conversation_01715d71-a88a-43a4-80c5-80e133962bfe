resource "kubernetes_secret" "docker_default" {
  metadata {
    name = "regcred"
  }

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "${var.registry_server}" = {
          auth = "${base64encode("${var.docker_username}:${var.docker_password}")}"
        }
      }
    })
  }

  type = "kubernetes.io/dockerconfigjson"
}

resource "kubernetes_secret" "docker_etl" {
  metadata {
    name      = "regcred"
    namespace = kubernetes_namespace.etl.id
  }

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "${var.registry_server}" = {
          auth = "${base64encode("${var.docker_username}:${var.docker_password}")}"
        }
      }
    })
  }

  type = "kubernetes.io/dockerconfigjson"
}

resource "kubernetes_secret" "docker_airbyte" {
  metadata {
    name      = "regcred"
    namespace = kubernetes_namespace.airbyte.id
  }

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "${var.registry_server}" = {
          auth = "${base64encode("${var.docker_username}:${var.docker_password}")}"
        }
      }
    })
  }

  type = "kubernetes.io/dockerconfigjson"
}

resource "kubernetes_secret" "docker_dl" {
  metadata {
    name      = "regcred"
    namespace = kubernetes_namespace_v1.lineage.id
  }

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "${var.registry_server}" = {
          auth = "${base64encode("${var.docker_username}:${var.docker_password}")}"
        }
      }
    })
  }

  type = "kubernetes.io/dockerconfigjson"
}

resource "kubernetes_secret" "docker_shopgpt" {
  metadata {
    name      = "regcred"
    namespace = kubernetes_namespace_v1.shopgpt.id
  }

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "${var.registry_server}" = {
          auth = "${base64encode("${var.docker_username}:${var.docker_password}")}"
        }
      }
    })
  }

  type = "kubernetes.io/dockerconfigjson"
}