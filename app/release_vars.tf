variable "airflow_vars" {
  default = {
    id_stitching_incremental_schedule_interval = "0 */4 * * *"
    emr_ec2_instance_type                      = "m4.large"
    events_incremental_schedule_interval       = "0 * * * *"
    schedule_interval_delete_idle_connections  = "0 */3 * * *"
    execution_window_in_days                   = "90"
    tag_dbt_analytics                          = "v0.25.3"
    tag_dbt_reverse_el                         = "v0.25.1"
    tag_superset_automation                    = "v0.24.0"
    user_reporting_schedule_interval           = "*/30 * * * *"
  }
}

variable "airbyte_v1" {
  default = {
    chart_version = "v1.0.0"
    global = {
      database = "airbyte"
    }

    bootloader = {
      image = {
        repository = "airbyte/bootloader"
        version    = "1.2.0"
      }
    }
    webapp = {
      image = {
        repository = "airbyte/webapp"
        version    = "1.2.0"
      }
    }
    minio = {
      image = {
        repository = "minio/minio"
        version    = "RELEASE.2024-11-07T00-52-20Z.fips"
      }
    }
    cron = {
      image = {
        repository = "airbyte/cron"
        version    = "1.2.0"
      }
    }
    server = {
      image = {
        repository = "airbyte/server"
        version    = "1.2.0"
      }
    }
    scheduler = {
      image = {
        repository = "blotout/airbyte-scheduler"
        version    = "1.2.0"
      }
    }
    worker = {
      image = {
        repository = "airbyte/worker"
        version    = "1.2.0"
      }
    }
    workload-launcher = {
      image = {
        repository = "airbyte/workload-launcher"
        version    = "1.2.0"
      }
    }
    container-orchestrator = {
      image = {
        repository = "airbyte/container-orchestrator"
        version    = "1.2.0"
      }
    }
    workload-init-container = {
      image = {
        repository = "airbyte/workload-init-container"
        version    = "1.2.0"
      }
    }
    connector-builder-server = {
      image = {
        repository = "airbyte/connector-builder-server"
        version    = "1.2.0"
      }
    }
    workload-api-server = {
      image = {
        repository = "airbyte/workload-api-server"
        version    = "1.2.0"
      }
    }
    metrics = {
      image = {
        repository = "airbyte/metrics-reporter"
        version    = "1.2.0"
      }
    }
    pod-sweeper = {
      image = {
        repository = "bitnami/kubectl"
        version    = "1.28.9"
      }
    }
  }
}

variable "airflow" {
  default = {
    chart_version = "19.0.4"
    scheduler = {
      image = {
        repository = "bitnami/airflow-scheduler"
        version    = "2.10.0"
      }
    }
    worker = {
      image = {
        repository = "bitnami/airflow-worker"
        version    = "2.10.0"
      }
    }
    web = {
      image = {
        repository = "bitnami/airflow"
        version    = "2.10.0"
      }
    }
    dags = {
      repository = {
        branch = "main"
        name   = "airflow-dags"
        path   = "dags"
      }
    }
  }
}

variable "dashboard_backend" {
  default = {
    chart_version = "v0.27.0"
    image = {
      repository = "blotout/dashboard-backend"
      version    = "v0.25.2"
    }
  }
}

variable "dashboard_frontend" {
  default = {
    chart_version = "v0.27.0"
    image = {
      repository = "blotout/cloud-app-ui"
      version    = "v0.25.1"
    }
  }
}

variable "shoptgpt_backend" {
  default = {
    chart_version = "v0.1.4"
    image = {
      repository = "blotout/shopgpt-backend"
      version    = "latest"
    }
  }
}

variable "infra_script" {
  default = {
    image = {
      repository = "blotout/infra_script"
      version    = "v0.25.0"
    }
  }
}

# data-linegea
variable "tenant" {
  default = "PASS"
}

variable "data_lineage" {
  default = {
    chart_version = "0.0.2"
    image = {
      repository = "blotout/dbt-analytics"
      version    = "data-lineage-aebb1ef3290253c857d5dbbacf7d1782969b572f"
    }
  }
}
