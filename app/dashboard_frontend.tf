# frontend deploy on Kubernetes. #

resource "helm_release" "frontend" {
  depends_on = [
    helm_release.ingress
  ]
  name       = "frontend-ui"
  repository = "https://blotoutio.github.io/charts"
  chart      = "dashboard-frontend"
  version    = var.dashboard_frontend["chart_version"]
  namespace  = "default"

  set {
    name  = "image.repository"
    value = var.dashboard_frontend["image"]["repository"]
  }
  set {
    name  = "image.tag"
    value = var.dashboard_frontend["image"]["version"]
  }
  set {
    name  = "autoscaling.enabled"
    value = "true"
    type  = "auto"
  }

  set {
    name  = "autoscaling.maxReplicas"
    value = "2"
  }

  set {
    name  = "env.API_BASE_URL"
    value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}"
  }

  set {
    name  = "env.SUPERSET_BASE_URL"
    value = "https://${data.aws_secretsmanager_secret_version.bi_domain.secret_string}"
  }

  set {
    name  = "env.AIRBYTE_DOC_BASE_URL"
    value = "https://docs-airbyte.blotout.io/"
  }

  set {
    name  = "ingress.enabled"
    value = true
  }

  set {
    name  = "ingress.hosts[0].host"
    value = data.aws_secretsmanager_secret_version.ui_domain.secret_string
  }

  set {
    name  = "ingress.annotations.nginx\\.ingress\\.kubernetes\\.io/server-snippet"
    value = <<EOF
      location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\\nDisallow: /\\n";
      }
      EOF
  }

  set {
    name  = "ingress.hosts[0].paths[0].path"
    value = "/"
  }

  set {
    name  = "ingress.hosts[0].paths[0].pathType"
    value = "ImplementationSpecific"
  }

  set {
    name  = "livenessProbe.httpGet.path"
    value = "/"
  }

  set {
    name  = "nameOverride"
    value = "blotout"
  }

  set {
    name  = "readinessProbe.httpGet.path"
    value = "/"
  }

  set {
    name  = "service.port"
    value = 80
  }

  set {
    name  = "imagePullSecrets[0].name"
    value = "regcred"
  }

  set {
    name  = "resources.limits.cpu"
    value = "30m"
  }

  set {
    name  = "resources.limits.memory"
    value = "60Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "10m"
  }

  set {
    name  = "resources.requests.memory"
    value = "15Mi"
  }
}
