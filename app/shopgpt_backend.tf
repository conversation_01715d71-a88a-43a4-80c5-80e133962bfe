# backend_api deploy on Kubernetes. #

resource "kubernetes_namespace_v1" "shopgpt" {
  metadata {
    name = "shopgpt"
  }
}

resource "kubernetes_config_map" "shopgpt-configmap" {
  metadata {
    name      = "shopgpt-configmap"
    namespace = kubernetes_namespace_v1.shopgpt.id
  }

  data = {
    SERVER_PORT                  = "8082"
    CUSTOM_AWS_ACCESS_KEY_ID     = "${var.aws_access_key}"
    CUSTOM_AWS_SECRET_ACCESS_KEY = "${var.aws_secret_key}"
    CUSTOM_AWS_DEFAULT_REGION    = "${var.aws_region}"
    AMAZON_ACCOUNT_ID            = "${data.aws_caller_identity.current.account_id}"
    ORGANIZATION_ID              = "${var.organization_name}"
    ENV                          = "${var.environment}"
    SCHEMA                       = "${var.organization_name}_${var.environment}"
    ATHENA_DATABASE              = "${var.organization_name}_${var.environment}"
    QUERY_LOGS_BUCKET            = "${aws_s3_bucket.athena_logs.bucket}"
    POSTGRESS_PORT               = "5432"
    POSTGRESS_DATABASE_NAME      = "shopgpt_staging"
    POSTGRESS_USER          = "${var.postgres_user}"
    REDIS_CLUSTER_NODES          = "${data.aws_secretsmanager_secret_version.redis.secret_string}:6379"
    ATHENA_ACCESS_KEY_ID         = "${var.aws_access_key}"
    ATHENA_SECRET_ACCES_KEY      = "${var.aws_secret_key}"
    ATHENA_REGION                = "${var.aws_region}"
    SMTP_HOST               = "${var.smtp_host}"
    SMTP_PORT               = "${var.smtp_port}"
    SMTP_USER               = "${var.smtp_user}"
    SMTP_PASSWORD           = "${var.smtp_password}"
    SMTP_SSL                = "${var.smtp_ssl}"
    SMTP_SOCKETFACTORY_PORT = "${var.smtp_socketfactory_port}"
    SMTP_MAIL_FROM          = "${var.smtp_mail_from}"
    MODEL                        = "gpt-4o-mini"
    VECTOR_INDEX_NAME            = "gte-small"
    OTEL_SERVICE_NAME            = "shopgpt_stagging"
    OTEL_EXPORTER_OTLP_PROTOCOL  = "${var.OTEL_EXPORTER_OTLP_PROTOCOL}"
    OTEL_EXPORTER_OTLP_ENDPOINT  = "${var.OTEL_EXPORTER_OTLP_ENDPOINT}"
    OTEL_API_KEY                 = "${var.OTEL_API_KEY}"
    OPENAI_API_KEY               = "${var.OPENAI_API_KEY}"
    PINECONE_API_KEY             = "${var.PINECONE_API_KEY}"
    PINECONE_ENVIRONMENT         = "dev"
    LANGFUSE_PUBLIC_KEY          = "${var.LANGFUSE_PUBLIC_KEY}"
    LANGFUSE_SECRET_KEY          = "${var.LANGFUSE_SECRET_KEY}"
    GROQ_API_KEY                 = "${var.GROQ_API_KEY}"
    HONEYCOMB_LOGS_ENDPOINT      = "${var.HONEYCOMB_LOGS_ENDPOINT}"
    LLM_API_KEY                  = "${var.LLM_API_KEY}"
    LITE_LLM_MODEL_NAME          = "gemini/gemini-2.0-flash"
    LITELLM_DEBUG                = false
    # DATABASE_URL                 = "postgresql://${var.postgres_user}:${data.aws_secretsmanager_secret_version.postgres_password.secret_string}@${data.aws_secretsmanager_secret_version.rds.secret_string}:5432/shopgpt_staging"

  }
}

resource "helm_release" "shopgpt_backend" {
  name = "shopgpt-backend"
  depends_on = [
    kubernetes_config_map.shopgpt-configmap,
    helm_release.ingress,
    helm_release.etl
  ]
  repository = "https://blotoutio.github.io/charts"
  chart      = "shopgpt-backend"
  version    = var.shoptgpt_backend["chart_version"]
  namespace  = kubernetes_namespace_v1.shopgpt.id
  lifecycle {
    replace_triggered_by = [
      # Replace `aws_appautoscaling_target` each time this instance of
      # the `aws_ecs_service` is replaced.
      kubernetes_config_map.shopgpt-configmap
    ]
  }

  set {
    name  = "image.repository"
    value = var.shoptgpt_backend["image"]["repository"]
  }
  set {
    name  = "image.tag"
    value = var.shoptgpt_backend["image"]["version"]
  }
  set {
    name  = "autoscaling.enabled"
    value = true
    type  = "auto"
  }

  set {
    name  = "autoscaling.targetCPUUtilizationPercentage"
    value = 50
  }

  set {
    name  = "autoscaling.targetMemoryUtilizationPercentage"
    value = 60
  }

  set {
    name  = "autoscaling.maxReplicas"
    value = "2"
  }

  set {
    name  = "imagePullSecrets[0].name"
    value = "regcred"
  }

  set {
    name  = "ingress.enabled"
    value = true
  }

  set {
    name  = "ingress.hosts[0].host"
    value = data.aws_secretsmanager_secret_version.ui_domain.secret_string
  }

  set {
    name  = "ingress.hosts[0].paths[0].path"
    value = "/shopgpt"
  }

  set {
    name  = "ingress.hosts[0].paths[0].pathType"
    value = "ImplementationSpecific"
  }

  set {
    name  = "livenessProbe.httpGet.port"
    value = 8082
  }

  set {
    name  = "livenessProbe.httpGet.path"
    value = "/shopgpt/v1/health"
  }

  set {
    name  = "nameOverride"
    value = "shopgpt"
  }

  set {
    name  = "fullnameOverride"
    value = "shopgpt"
  }

  set {
    name  = "readinessProbe.httpGet.path"
    value = "/shopgpt/v1/health"
  }

  set {
    name  = "readinessProbe.httpGet.port"
    value = 8082
  }

  set {
    name  = "service.port"
    value = 8082
  }

  set {
    name  = "resources.limits.cpu"
    value = "1000m"
  }

  set {
    name  = "resources.limits.memory"
    value = "4Gi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "200m"
  }

  set {
    name  = "resources.requests.memory"
    value = "400Mi"
  }

}