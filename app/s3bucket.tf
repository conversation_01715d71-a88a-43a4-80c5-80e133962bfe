# Create LANDING_BUCKET Bucket
resource "aws_s3_bucket" "landing_bucket" {
  bucket        = "b-${var.organization_name}-${var.environment}-landing"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Store landing object"
  }
}
resource "aws_s3_bucket_ownership_controls" "landing_bucket" {
  bucket = aws_s3_bucket.landing_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "landing_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.landing_bucket]
  bucket     = aws_s3_bucket.landing_bucket.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "landing_bucket" {
  bucket = aws_s3_bucket.landing_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "landing_bucket" {
  bucket = aws_s3_bucket.landing_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Create STAGING_BUCKET Bucket
resource "aws_s3_bucket" "staging_bucket" {
  bucket        = "b-${var.organization_name}-${var.environment}-stg"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Store staging object"
  }
}
resource "aws_s3_bucket_ownership_controls" "staging_bucket" {
  bucket = aws_s3_bucket.staging_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "staging_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.staging_bucket]
  bucket     = aws_s3_bucket.staging_bucket.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "staging_bucket" {
  bucket = aws_s3_bucket.staging_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "staging_bucket" {
  bucket = aws_s3_bucket.staging_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Create PROCESSED_BUCKET Bucket
resource "aws_s3_bucket" "processed_bucket" {
  bucket        = "b-${var.organization_name}-${var.environment}-processed"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Store processed object"
  }
}
resource "aws_s3_bucket_ownership_controls" "processed_bucket" {
  bucket = aws_s3_bucket.processed_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "processed_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.processed_bucket]
  bucket     = aws_s3_bucket.processed_bucket.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "processed_bucket" {
  bucket = aws_s3_bucket.processed_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "processed_bucket" {
  bucket = aws_s3_bucket.processed_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Create OUTBOUND_BUCKET Bucket
resource "aws_s3_bucket" "outbound_bucket" {
  bucket        = "b-${var.organization_name}-${var.environment}-outbound"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Object moving out of lake"
  }
}
resource "aws_s3_bucket_ownership_controls" "outbound_bucket" {
  bucket = aws_s3_bucket.outbound_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "outbound_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.outbound_bucket]
  bucket     = aws_s3_bucket.outbound_bucket.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "outbound_bucket" {
  bucket = aws_s3_bucket.outbound_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "outbound_bucket" {
  bucket = aws_s3_bucket.outbound_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Create Athena Logs Bucket
resource "aws_s3_bucket" "athena_logs" {
  bucket        = "b-${var.organization_name}-${var.environment}-athena-logs"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Store athena logs"
  }
}
resource "aws_s3_bucket_ownership_controls" "athena_logs" {
  bucket = aws_s3_bucket.athena_logs.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "athena_logs" {
  depends_on = [aws_s3_bucket_ownership_controls.athena_logs]
  bucket     = aws_s3_bucket.athena_logs.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "athena_logs" {
  bucket = aws_s3_bucket.athena_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "athena_logs" {
  bucket = aws_s3_bucket.athena_logs.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
resource "aws_s3_bucket_lifecycle_configuration" "athena_logs" {
  bucket = aws_s3_bucket.athena_logs.id
  rule {
    id = "log-rotation-7-day-rule"
    noncurrent_version_expiration {
      noncurrent_days = 7
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
    expiration {
      days = 7
    }
    status = "Enabled"
  }
}

# Create Airbyte Logs Bucket
resource "aws_s3_bucket" "airbyte_logs" {
  bucket        = "b-${var.organization_name}-${var.environment}-airbyte-logs"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "Store airbyte logs"
  }
}
resource "aws_s3_bucket_ownership_controls" "airbyte_logs" {
  bucket = aws_s3_bucket.airbyte_logs.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "airbyte_logs" {
  depends_on = [aws_s3_bucket_ownership_controls.airbyte_logs]
  bucket     = aws_s3_bucket.airbyte_logs.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "airbyte_logs" {
  bucket = aws_s3_bucket.airbyte_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "airbyte_logs" {
  bucket = aws_s3_bucket.airbyte_logs.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
resource "aws_s3_bucket_lifecycle_configuration" "airbyte_logs" {
  bucket = aws_s3_bucket.airbyte_logs.id
  rule {
    id = "log-rotation-7-day-rule"
    noncurrent_version_expiration {
      noncurrent_days = 7
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
    expiration {
      days = 7
    }
    status = "Enabled"
  }
}

# Create EMR Bucket
resource "aws_s3_bucket" "emr_bucket" {
  bucket        = "b-${var.organization_name}-${var.environment}-emr"
  force_destroy = true
  tags = {
    Name        = "${var.organization_name}"
    Environment = "${var.environment}"
    Description = "EMR application configuration"
  }
}
resource "aws_s3_bucket_ownership_controls" "emr_bucket" {
  bucket = aws_s3_bucket.emr_bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}
resource "aws_s3_bucket_acl" "emr_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.emr_bucket]
  bucket     = aws_s3_bucket.emr_bucket.id
  acl        = "private"
}
resource "aws_s3_bucket_public_access_block" "emr_bucket" {
  bucket = aws_s3_bucket.emr_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_server_side_encryption_configuration" "emr_bucket" {
  bucket = aws_s3_bucket.emr_bucket.bucket
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
resource "aws_s3_bucket_lifecycle_configuration" "emr_bucket" {
  bucket = aws_s3_bucket.emr_bucket.id
  rule {
    id = "log-rotation-7-day-rule"
    filter {
      prefix = "elasticmapreduce/"
    }
    noncurrent_version_expiration {
      noncurrent_days = 7
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
    expiration {
      days = 7
    }
    status = "Enabled"
  }
}