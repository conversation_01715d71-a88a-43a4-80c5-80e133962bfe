# data lineage deploy on Kubernetes. #

resource "kubernetes_namespace_v1" "lineage" {
  metadata {
    annotations = {
      name = "lineage"
    }
    name = "lineage"
  }
}

resource "kubernetes_config_map" "lineage-configmap" {
  depends_on = [
    kubernetes_namespace_v1.lineage
  ]
  metadata {
    name      = "lineage-configmap"
    namespace = kubernetes_namespace_v1.lineage.id
  }

  data = {
    S3_STAGING_DIR        = "s3://${aws_s3_bucket.athena_logs.bucket}/dbt/"
    REGION_NAME           = "${var.aws_region}"
    SCHEMA                = "${var.organization_name}_${var.environment}"
    BUCKET                = aws_s3_bucket.processed_bucket.arn
    DATABASE              = "awsdatacatalog"
    ORG                   = "${var.organization_name}"
    ENV                   = "${var.environment}"
    TENANT                = "${var.tenant}"
    AWS_ACCESS_KEY_ID     = "${var.aws_access_key}"
    AWS_SECRET_ACCESS_KEY = "${var.aws_secret_key}"
    DBT_PROFILES_DIR      = "/dbt"
  }
}

resource "helm_release" "data-lineage" {
  name = "data-lineage"
  depends_on = [
    kubernetes_config_map.lineage-configmap
  ]
  repository = "https://blotoutio.github.io/charts"
  chart      = "data-lineage"
  version    = var.data_lineage["chart_version"]
  namespace  = kubernetes_namespace_v1.lineage.id
  lifecycle {
    replace_triggered_by = [
      # Replace `aws_appautoscaling_target` each time this instance of
      # the `aws_ecs_service` is replaced.
      kubernetes_config_map.lineage-configmap
    ]
  }

  set {
    name  = "image.repository"
    value = var.data_lineage["image"]["repository"]
  }
  set {
    name  = "image.tag"
    value = var.data_lineage["image"]["version"]
  }
  set {
    name  = "autoscaling.enabled"
    value = "false"
    type  = "auto"
  }
  set {
    name  = "nameOverride"
    value = "data-lineage"
  }
  set {
    name  = "fullnameOverride"
    value = "data-lineage"
  }
  set {
    name  = "service.port"
    value = 8081
  }
  set {
    name  = "imagePullSecrets[0].name"
    value = "regcred"
  }
  set {
    name  = "env.SYNC_OUTBOUND_DATA_START_DATE"
    value = formatdate("YYYY-MM-DD", timeadd(timestamp(), "-24h"))
  }
  set {
    name  = "env.SYNC_OUTBOUND_DATA_END_DATE"
    value = formatdate("YYYY-MM-DD", timeadd(timestamp(), "-24h"))
  }
  set {
    name  = "env.EXECUTION_DATE"
    value = formatdate("YYYY-MM-DD", timeadd(timestamp(), "0h"))
  }
  set {
    name  = "env.EXECUTION_END_DATE"
    value = formatdate("YYYY-MM-DD", timeadd(timestamp(), "0h"))
  }
  set {
    name  = "env.EXECUTION_START_DATE"
    value = formatdate("YYYY-MM-DD", timeadd(timestamp(), "-1440h"))
  }
}