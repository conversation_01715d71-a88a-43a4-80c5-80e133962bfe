resource "aws_iam_role" "glue" {
  name        = "glue_role_${var.organization_name}_${var.environment}"
  description = "IAM Role for Kenisis Firehose"

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "glue.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "glue"
    Description  = "IAM Role for glue"
  }
}

resource "aws_iam_policy" "glue_policy" {
  name        = "glue_policy_${var.organization_name}_${var.environment}"
  description = "glue policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "glue:*",
          "s3:GetBucketLocation",
          "s3:ListBucket",
          "s3:ListAllMyBuckets",
          "s3:GetBucketAcl",
          "ec2:DescribeVpcEndpoints",
          "ec2:DescribeRouteTables",
          "ec2:CreateNetworkInterface",
          "ec2:DeleteNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeSubnets",
          "ec2:DescribeVpcAttribute",
          "iam:ListRolePolicies",
          "iam:GetRole",
          "iam:GetRolePolicy",
          "cloudwatch:PutMetricData",
          "s3:Get*",
          "s3:List*",
          "s3:*"
        ]
        Resource = [
          "*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:CreateBucket"
        ]
        Resource = [
          "arn:aws:s3:::aws-glue-*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::aws-glue-*/*",
          "arn:aws:s3:::*/*aws-glue-*/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject"
        ]
        "Resource" : [
          "arn:aws:s3:::crawler-public*",
          "arn:aws:s3:::aws-glue-*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:AssociateKmsKey"
        ]
        Resource = [
          "arn:aws:logs:*:*:/aws-glue/*"
        ]
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "glue_role_attachment" {
  role       = aws_iam_role.glue.name
  policy_arn = aws_iam_policy.glue_policy.arn
}

resource "aws_sns_topic" "glue_tables" {
  name = "glue_crawler_notification"
}

resource "aws_sns_topic_subscription" "subscription_one" {
  topic_arn = aws_sns_topic.glue_tables.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}
resource "aws_sns_topic_subscription" "subscription_two" {
  topic_arn = aws_sns_topic.glue_tables.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_cloudwatch_event_rule" "console" {
  name = "glue_crwaler_notifaction"
  event_pattern = jsonencode({

    "source" : ["aws.glue"],
    "detail-type" : ["Glue Crawler State Change"],
    "detail" : {
      "state" : ["Failed"]
    }


  })
}

resource "aws_cloudwatch_event_target" "sns" {
  rule      = aws_cloudwatch_event_rule.console.name
  target_id = "SendToSNS"
  arn       = aws_sns_topic.glue_tables.arn
}