locals {
  ui_domain                     = "${var.organization_name}-ui-${var.environment}.blotout.io"
  airbyte_domain                = "${var.organization_name}-${var.environment}-airbyte.blotout.io"
  superset_domain               = "${var.organization_name}-bi-${var.environment}.blotout.io"
  k8s_service_account_name      = "cluster-autoscaler"
  k8s_service_account_namespace = "kube-system"
  eks_oidc_issuer               = trimprefix(data.aws_eks_cluster.eks_cluster.identity[0].oidc[0].issuer, "https://")
}