resource "kubernetes_job" "update_variable" {
  depends_on = [
    helm_release.airbyte_v1,
    helm_release.backend_api,
    helm_release.etl
  ]
  metadata {
    name      = "update-variable"
    namespace = "default"
  }
  spec {
    template {
      metadata {}
      spec {
        image_pull_secrets {
          name = "regcred"
        }
        container {
          name              = "update-variable"
          image             = "${var.infra_script["image"]["repository"]}:${var.infra_script["image"]["version"]}"
          image_pull_policy = "IfNotPresent"
          command           = ["python3", "insert.py"]
          env {
            name  = "POSTGRES_ENDPOINT"
            value = data.aws_secretsmanager_secret_version.rds.secret_string
          }
          env {
            name  = "POSTGRES_USER"
            value = var.postgres_user
          }
          env {
            name  = "POSTGRES_PASSWORD"
            value = data.aws_secretsmanager_secret_version.postgres_password.secret_string
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__AIRFLOW_START_DATE"
            value = var.airflow_job_start_date
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__AWS_REGION"
            value = var.aws_region
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__AIRFLOW_DAG_FAILED_EMAIL"
            value = var.failure_report_email
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__PRIVATE_SUBNET"
            value = data.aws_secretsmanager_secret_version.subnet_private.secret_string
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__AIRBYTE_URL"
            value = "https://${data.aws_secretsmanager_secret_version.ui_domain.secret_string}"
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__SUPERSET_BASE_URL"
            value = "https://${data.aws_secretsmanager_secret_version.bi_domain.secret_string}/"
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__SUPERSET_USERNAME"
            value = var.superset_admin
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__SUPERSET_USER_EMAIL"
            value = var.superset_admin
          }
          env {
            name  = "POSTGRES__AIRFLOW__VARIABLE__SUPERSET_PASSWORD"
            value = data.aws_secretsmanager_secret_version.superset_password.secret_string
          }
          env {
            name  = "POSTGRES__BLOTOUT__VALIDATE_EMAIL__ADMIN_EMAIL"
            value = var.administrator_email
          }
          dynamic "env" {
            for_each = var.airflow_vars
            content {
              name  = "POSTGRES__AIRFLOW__VARIABLE__${upper(env.key)}"
              value = env.value
            }
          }
        }
        restart_policy = "Never"
      }
    }
    backoff_limit = 4
  }
  wait_for_completion = false
}
