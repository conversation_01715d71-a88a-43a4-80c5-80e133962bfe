# EKS Cluster
variable "aws_region" {
  default = ""
}

variable "aws_access_key" {
  default = ""
}

variable "aws_secret_key" {
  default = ""
}

variable "organization_name" {
  default = ""
}

variable "environment" {
  default     = ""
  description = "define your environment ex. dev or prod"
}

variable "eks_version" {
  default     = "1.31"
  description = "define k8s version"
}

variable "eks_addons" {
  default = {
    coredns = {
      name                        = "coredns"
      version                     = "v1.11.4-eksbuild.2"
      resolve_conflicts_on_update = "OVERWRITE"
    },
    vpc-cni = {
      name                        = "vpc-cni"
      version                     = "v1.19.4-eksbuild.1"
      resolve_conflicts_on_update = "OVERWRITE"
    },
    kube-proxy = {
      name                        = "kube-proxy"
      version                     = "v1.30.6-eksbuild.3"
      resolve_conflicts_on_update = "OVERWRITE"
    },
    aws-ebs-csi-driver = {
      name                        = "aws-ebs-csi-driver"
      version                     = "v1.44.0-eksbuild.1"
      resolve_conflicts_on_update = "OVERWRITE"
    }
  }

}

variable "ec2_instance_type" {
  default = ["t3.xlarge"]
  type    = list(string)
}

variable "disk_size" {
  default     = "40"
  description = "Default Size in GB"
}


# RDS
variable "postgres_user" {
  default = "rds_admin"
}

variable "db_disk_size" {
  default = "20"
}

variable "db_instance_type" {
  default     = "db.t3.small"
  description = "The Database Instance Type"
  type        = string
}

variable "db_engine_version" {
  default = "13.20"
}

variable "db_family" {
  default = "postgres13"
}

variable "multi-az-deployment" {
  default     = false
  description = "Create a Standby DB Instance"
  type        = bool
}

variable "aws_db_option_group_name" {
  default = "default:postgres-13"
}

# ElastiCache
variable "family" {
  default     = "redis5.0"
  type        = string
  description = "The family of the ElastiCache parameter group."
}

variable "engine_version" {
  default     = "5.0.6"
  type        = string
  description = "The version number of the cache engine to be used for the cache clusters in this replication group."
}

variable "elastic_cache_node_type" {
  description = "The compute and memory capacity of the nodes in the node group. See https://aws.amazon.com/elasticache/pricing/ for the list of available node types."
  default     = "cache.t2.micro"
}
