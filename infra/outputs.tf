output "region" {
  value = var.aws_region
}

output "organization_name" {
  value = var.organization_name
}

output "account_id" {
  value = data.aws_caller_identity.current.account_id
}

# output "loadbalancer" {
#   value = data.aws_lb.lb.dns_name
# }

output "environment" {
  value = var.environment
}
output "eks_cluster" {
  value = {
    "name" : aws_eks_cluster.eks_cluster.name,
    "version" : aws_eks_cluster.eks_cluster.version
  }
}

output "rds_details" {
  value = {
    "name" : "${var.organization_name}-${var.environment}",
    "endpoint" : aws_db_instance.db_instance.endpoint,
    "engine" : aws_db_instance.db_instance.engine_version_actual
  }
}