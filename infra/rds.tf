resource "aws_db_instance" "db_instance" {
  identifier                   = "${var.organization_name}-${var.environment}"
  instance_class               = var.db_instance_type
  skip_final_snapshot          = true
  multi_az                     = var.multi-az-deployment
  vpc_security_group_ids       = [aws_security_group.allow_tls.id]
  storage_type                 = "gp3"
  engine                       = "postgres"
  engine_version               = var.db_engine_version
  option_group_name            = var.aws_db_option_group_name
  db_name                      = "blotout"
  username                     = var.postgres_user
  password                     = random_password.postgres_password.result
  parameter_group_name         = aws_db_parameter_group.default.name
  db_subnet_group_name         = aws_db_subnet_group.db_subnet_group.name
  publicly_accessible          = false
  depends_on                   = [aws_db_subnet_group.db_subnet_group]
  allocated_storage            = var.db_disk_size
  max_allocated_storage        = "1000"
  apply_immediately            = false
  backup_window                = "03:00-06:00"
  backup_retention_period      = "7"
  deletion_protection          = false
  performance_insights_enabled = true
  auto_minor_version_upgrade   = true
  copy_tags_to_snapshot        = true
  license_model                = "postgresql-license"
  port                         = "5432"

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "RDS Cluster"
  }
}

resource "aws_db_parameter_group" "default" {
  name        = "${var.organization_name}-${var.environment}"
  family      = var.db_family
  description = "Parameter Group for Database Instance"
}

# Create Database Subnet Group
resource "aws_db_subnet_group" "db_subnet_group" {
  name        = "${var.organization_name}_${var.environment}"
  subnet_ids  = aws_subnet.private_subnet[*].id
  description = "Database Subnet for ${var.organization_name}_${var.environment}"

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Security Group"
  }
}

resource "aws_security_group" "allow_tls" {
  name        = "allow_tls"
  description = "Allow TLS inbound traffic"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.vpc.cidr_block]
    description = "Allow only EKS to connect"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    # description = "Allow outgoing traffic from RDS"
  }

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Security Group"
  }
}
