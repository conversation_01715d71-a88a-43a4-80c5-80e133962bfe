/*==== The VPC ======*/
resource "aws_vpc" "vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name                                                                = "${var.organization_name}_${var.environment}"
    Cluster-name                                                        = var.organization_name
    Environment                                                         = var.environment
    Resource                                                            = "VPC"
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "shared"
  }
}

/*==== Subnets ======*/
/* Internet gateway for the public subnet */
resource "aws_internet_gateway" "ig" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Internet Gateway"
  }
}

/* Elastic IP for NAT */
resource "aws_eip" "nat_eip" {
  domain     = "vpc"
  depends_on = [aws_internet_gateway.ig]
  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Elastic IP addresses"
  }
}

/* NAT Gateway */
resource "aws_nat_gateway" "nat_gw" {
  allocation_id     = aws_eip.nat_eip.id
  subnet_id         = aws_subnet.public_subnet[1].id
  depends_on        = [aws_internet_gateway.ig]
  connectivity_type = "public"

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "NAT gateway"
  }
}

/* Public Subnet */
resource "aws_subnet" "public_subnet" {
  count                   = 2
  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = "10.0.${count.index}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name                                                                = "${var.organization_name}_${var.environment}"
    Cluster-name                                                        = var.organization_name
    Environment                                                         = var.environment
    Resource                                                            = "Public Subnet"
    Availability_Zone                                                   = data.aws_availability_zones.available.names[count.index]
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "shared"
  }
}

/* Private Subnet */
resource "aws_subnet" "private_subnet" {
  count                   = 2
  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = "10.0.${count.index + 25}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = false

  tags = {
    Name                                                                = "${var.organization_name}_${var.environment}"
    Cluster-name                                                        = var.organization_name
    Environment                                                         = var.environment
    Resource                                                            = "Private Subnet"
    Availability_Zone                                                   = data.aws_availability_zones.available.names[count.index]
    "kubernetes.io/cluster/${var.organization_name}_${var.environment}" = "shared"
  }
}

/* Routing table for private subnet */
resource "aws_route_table" "private" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Route Table Private"
  }
}

/* Routing table for public subnet */
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Route Table Public"
  }
}

resource "aws_route" "public_internet_gateway" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.ig.id
}

resource "aws_route" "private_nat_gateway" {
  route_table_id         = aws_route_table.private.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat_gw.id
}

/* Route table associations */
resource "aws_route_table_association" "private" {
  count          = 2
  subnet_id      = aws_subnet.private_subnet.*.id[count.index]
  route_table_id = aws_route_table.private.id
}

resource "aws_route_table_association" "public" {
  count          = 2
  subnet_id      = aws_subnet.public_subnet.*.id[count.index]
  route_table_id = aws_route_table.public.id
}
