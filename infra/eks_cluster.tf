/* Creates an IAM role */
resource "aws_iam_role" "iam_role_cluster" {
  name = "${var.organization_name}_${var.environment}-iam-eks"

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "eks.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "cluster-AmazonEKSClusterPolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.iam_role_cluster.name
}

resource "aws_iam_role_policy_attachment" "cluster-AmazonEKSVPCResourceController" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController"
  role       = aws_iam_role.iam_role_cluster.name
}

resource "aws_security_group" "security_group" {
  name        = "${var.organization_name}_${var.environment}-sg"
  description = "Cluster communication with worker nodes"
  vpc_id      = aws_vpc.vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Security Group"
    Description  = "Worker nodes security group"
  }
}

resource "aws_security_group_rule" "cluster-ingress-workstation-https" {
  cidr_blocks       = ["0.0.0.0/0"]
  description       = "Allow workstation to communicate with the cluster API Server"
  from_port         = 443
  protocol          = "tcp"
  security_group_id = aws_security_group.security_group.id
  to_port           = 443
  type              = "ingress"
}

/* Provision an EKS Cluster */
resource "aws_eks_cluster" "eks_cluster" {
  name     = "${var.organization_name}_${var.environment}"
  role_arn = aws_iam_role.iam_role_cluster.arn
  version  = var.eks_version

  vpc_config {
    security_group_ids = [aws_security_group.security_group.id]
    subnet_ids         = aws_subnet.private_subnet.*.id
  }

  depends_on = [
    aws_iam_role_policy_attachment.cluster-AmazonEKSClusterPolicy,
    aws_iam_role_policy_attachment.cluster-AmazonEKSVPCResourceController
  ]
}
