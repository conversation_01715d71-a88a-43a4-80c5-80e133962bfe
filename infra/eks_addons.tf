resource "aws_eks_addon" "coredns" {
  cluster_name                = aws_eks_cluster.eks_cluster.name
  addon_name                  = var.eks_addons["coredns"]["name"]
  addon_version               = var.eks_addons["coredns"]["version"]
  resolve_conflicts_on_update = var.eks_addons["coredns"]["resolve_conflicts_on_update"]
}

resource "aws_eks_addon" "vpc-cni" {
  cluster_name                = aws_eks_cluster.eks_cluster.name
  addon_name                  = var.eks_addons["vpc-cni"]["name"]
  addon_version               = var.eks_addons["vpc-cni"]["version"]
  resolve_conflicts_on_update = var.eks_addons["vpc-cni"]["resolve_conflicts_on_update"]
}

resource "aws_eks_addon" "kube-proxy" {
  cluster_name                = aws_eks_cluster.eks_cluster.name
  addon_name                  = var.eks_addons["kube-proxy"]["name"]
  addon_version               = var.eks_addons["kube-proxy"]["version"]
  resolve_conflicts_on_update = var.eks_addons["kube-proxy"]["resolve_conflicts_on_update"]
}

# # Amazon EBS CSI Driver
resource "aws_eks_addon" "aws-ebs-csi-driver" {
  cluster_name                = aws_eks_cluster.eks_cluster.name
  addon_name                  = var.eks_addons["aws-ebs-csi-driver"]["name"]
  addon_version               = var.eks_addons["aws-ebs-csi-driver"]["version"]
  resolve_conflicts_on_update = var.eks_addons["aws-ebs-csi-driver"]["resolve_conflicts_on_update"]
  service_account_role_arn    = aws_iam_role.aws-ebs-csi-driver_role_node.arn
}

# # Amazon EBS CSI Driver Role
resource "aws_iam_role" "aws-ebs-csi-driver_role_node" {
  name = "AmazonEKS_EBS_CSI_DriverRole_${var.organization_name}_${var.environment}"

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/oidc.eks.${var.aws_region}.amazonaws.com/id/${trimprefix(aws_eks_cluster.eks_cluster.identity[0].oidc[0].issuer, "https://oidc.eks.${var.aws_region}.amazonaws.com/id/")}"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "oidc.eks.${var.aws_region}.amazonaws.com/id/${trimprefix(aws_eks_cluster.eks_cluster.identity[0].oidc[0].issuer, "https://oidc.eks.${var.aws_region}.amazonaws.com/id/")}:aud": "sts.amazonaws.com",
          "oidc.eks.${var.aws_region}.amazonaws.com/id/${trimprefix(aws_eks_cluster.eks_cluster.identity[0].oidc[0].issuer, "https://oidc.eks.${var.aws_region}.amazonaws.com/id/")}:sub": "system:serviceaccount:kube-system:ebs-csi-controller-sa"
        }
      }
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "aws-ebs-csi-driver_policy" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
  role       = aws_iam_role.aws-ebs-csi-driver_role_node.name
}