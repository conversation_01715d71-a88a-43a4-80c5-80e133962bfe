resource "aws_vpc_endpoint" "s3_endpoint" {
  vpc_id            = aws_vpc.vpc.id
  service_name      = "com.amazonaws.${var.aws_region}.s3"
  vpc_endpoint_type = "Gateway"
  policy            = "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}"

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "VPC Endpoint"
  }
}

resource "aws_vpc_endpoint_route_table_association" "s3_route_table_association" {
  route_table_id  = aws_route_table.private.id
  vpc_endpoint_id = aws_vpc_endpoint.s3_endpoint.id
}
