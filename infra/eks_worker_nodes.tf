resource "aws_iam_role" "iam_role_node" {
  name = "${var.organization_name}_${var.environment}-iam-ec2"

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "node-AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.iam_role_node.name
}
resource "aws_iam_role_policy_attachment" "AmazonS3FullAccess" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
  role       = aws_iam_role.iam_role_node.name
}
resource "aws_iam_role_policy_attachment" "node-AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.iam_role_node.name
}

resource "aws_iam_role_policy_attachment" "node-AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.iam_role_node.name
}

resource "aws_iam_role_policy_attachment" "ec2volume_mount" {
  policy_arn = aws_iam_policy.policy_volume.arn
  role       = aws_iam_role.iam_role_node.name
}

resource "aws_iam_policy" "policy_volume" {
  name   = "${var.organization_name}_${var.environment}-volume"
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:AttachVolume",
        "ec2:DescribeVolumes",
        "ec2:DescribeInstances",
        "ec2:DetachVolume",
        "ec2:CreateTags",
        "ec2:ModifyVolume",
        "ec2:DescribeVolumeStatus",
        "ec2:DescribeAvailabilityZones",
        "ec2:DescribeTags"
      ],
      "Resource": "*"
    }
  ]
}
EOF
}

resource "aws_eks_node_group" "node_group" {
  cluster_name    = aws_eks_cluster.eks_cluster.name
  node_group_name = "managed-node"
  node_role_arn   = aws_iam_role.iam_role_node.arn
  instance_types  = var.ec2_instance_type
  subnet_ids      = aws_subnet.private_subnet[*].id
  disk_size       = var.disk_size
  version         = var.eks_version
  # remote_access {
  #   ec2_ssh_key = "${var.organization_name}_${var.environment}"
  # }

  scaling_config {
    desired_size = 2
    max_size     = 10
    min_size     = 2
  }

  lifecycle {
    ignore_changes = [scaling_config[0].desired_size]
  }

  depends_on = [
    aws_iam_role_policy_attachment.node-AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.node-AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node-AmazonEC2ContainerRegistryReadOnly,
  ]

  labels = {
    "alpha.eksctl.io/nodegroup-name" = "managed-node",
    "alpha.eksctl.io/cluster-name"   = "${var.organization_name}_${var.environment}"
  }
}
