resource "aws_secretsmanager_secret" "rds" {
  name                    = "rds_endpoint"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "rds" {
  secret_id     = aws_secretsmanager_secret.rds.id
  secret_string = aws_db_instance.db_instance.address
}

resource "aws_secretsmanager_secret" "redis" {
  name                    = "redis_endpoint"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "redis" {
  secret_id     = aws_secretsmanager_secret.redis.id
  secret_string = aws_elasticache_replication_group.default.configuration_endpoint_address
}

resource "aws_secretsmanager_secret" "subnet_private" {
  name                    = "subnet_private"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "subnet_private" {
  secret_id     = aws_secretsmanager_secret.subnet_private.id
  secret_string = aws_subnet.private_subnet[0].id
}

resource "random_password" "postgres_password" {
  length      = 16
  special     = false
  min_upper   = 1
  min_lower   = 1
  min_numeric = 1
}

resource "aws_secretsmanager_secret" "postgres_password" {
  name                    = "postgres_password"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "postgres_password" {
  secret_id     = aws_secretsmanager_secret.postgres_password.id
  secret_string = random_password.postgres_password.result
}


resource "random_password" "airflow_password" {
  length      = 16
  special     = false
  min_upper   = 1
  min_lower   = 1
  min_numeric = 1
}

resource "aws_secretsmanager_secret" "airflow_password" {
  name                    = "airflow_password"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "airflow_password" {
  secret_id     = aws_secretsmanager_secret.airflow_password.id
  secret_string = random_password.airflow_password.result
}

resource "aws_secretsmanager_secret" "ui_domain" {
  name                    = "ui_domain"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "ui_domain" {
  secret_id     = aws_secretsmanager_secret.ui_domain.id
  secret_string = local.ui_domain
}

resource "aws_secretsmanager_secret" "airbyte_domain" {
  name                    = "airbyte_domain"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "airbyte_domain" {
  secret_id     = aws_secretsmanager_secret.airbyte_domain.id
  secret_string = local.airbyte_domain
}

resource "aws_secretsmanager_secret" "bi_domain" {
  name                    = "bi_domain"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "bi_domain" {
  secret_id     = aws_secretsmanager_secret.bi_domain.id
  secret_string = local.superset_domain
}
resource "random_password" "superset_password" {
  length      = 16
  special     = false
  min_upper   = 1
  min_lower   = 1
  min_numeric = 1
}

resource "aws_secretsmanager_secret" "superset_password" {
  name                    = "superset_password"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "superset_password" {
  secret_id     = aws_secretsmanager_secret.superset_password.id
  secret_string = random_password.superset_password.result
}