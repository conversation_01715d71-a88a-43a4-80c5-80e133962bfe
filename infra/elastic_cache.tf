resource "aws_elasticache_replication_group" "default" {
  description                 = "aws elasticache replication group"
  engine                      = "redis"
  parameter_group_name        = "default.redis5.0.cluster.on"
  subnet_group_name           = aws_elasticache_subnet_group.default.name
  security_group_ids          = [aws_security_group.sg_redis.id]
  replication_group_id        = "${var.organization_name}-${var.environment}"
  preferred_cache_cluster_azs = [data.aws_availability_zones.available.names[0], data.aws_availability_zones.available.names[1]]
  # number_cache_clusters    = 2
  node_type                = var.elastic_cache_node_type
  engine_version           = var.engine_version
  port                     = 6379
  maintenance_window       = "sun:05:00-sun:06:00"
  snapshot_window          = "00:00-05:00"
  snapshot_retention_limit = "5"

  num_node_groups         = 1
  replicas_per_node_group = 1

  automatic_failover_enabled = true
  auto_minor_version_upgrade = true
  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Replication Group"
    Description  = "replication_group"
  }
}

resource "aws_elasticache_subnet_group" "default" {
  name        = "${var.organization_name}-${var.environment}"
  subnet_ids  = [aws_subnet.private_subnet[0].id, aws_subnet.private_subnet[1].id]
  description = "aws elasticache subnet group"

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Subnet Group"
    Description  = "Subnet Group"
  }
}
resource "aws_security_group" "sg_redis" {
  vpc_id = aws_vpc.vpc.id

  ingress {
    description = "Allow only EKS to connect"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name         = "${var.organization_name}_${var.environment}"
    Cluster-name = var.organization_name
    Environment  = var.environment
    Resource     = "Security Group"
    Description  = "Worker nodes security group"
  }

}
